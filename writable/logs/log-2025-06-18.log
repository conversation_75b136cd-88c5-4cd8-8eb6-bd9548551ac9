CRITICAL - 2025-06-18 13:59:21 --> Class "App\Models\Users_model" not found
in APPPATH/Controllers/Home.php on line 30.
 1 SYSTEMPATH/CodeIgniter.php(914): App\Controllers\Home->__construct()
 2 SYSTEMPATH/CodeIgniter.php(493): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
ERROR - 2025-06-18 13:59:31 --> mysqli_sql_exception: Table 'trogonapps_thredex.settings' doesn't exist in /home/<USER>/apps/thredex/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /home/<USER>/apps/thredex/system/Database/MySQLi/Connection.php(306): mysqli->query('SELECT *\nFROM `...', 0)
#1 /home/<USER>/apps/thredex/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 /home/<USER>/apps/thredex/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 /home/<USER>/apps/thredex/system/Database/BaseBuilder.php(1748): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /home/<USER>/apps/thredex/app/Helpers/settings_helper.php(8): CodeIgniter\Database\BaseBuilder->getWhere(Array)
#5 /home/<USER>/apps/thredex/app/Helpers/settings_helper.php(65): get_settings('system_title')
#6 /home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php(6): get_site_title()
#7 /home/<USER>/apps/thredex/app/Views/Admin/index.php(2): include_once('/home/<USER>')
#8 /home/<USER>/apps/thredex/system/View/View.php(228): include('/home/<USER>')
#9 /home/<USER>/apps/thredex/system/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#10 /home/<USER>/apps/thredex/system/Common.php(1190): CodeIgniter\View\View->render('Admin/index', Array, true)
#11 /home/<USER>/apps/thredex/app/Controllers/Admin/Dashboard.php(18): view('Admin/index', Array)
#12 /home/<USER>/apps/thredex/system/CodeIgniter.php(942): App\Controllers\Admin\Dashboard->index()
#13 /home/<USER>/apps/thredex/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Dashboard))
#14 /home/<USER>/apps/thredex/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#15 /home/<USER>/public_html/thredex/index.php(83): CodeIgniter\CodeIgniter->run()
#16 {main}
CRITICAL - 2025-06-18 13:59:31 --> Table 'trogonapps_thredex.settings' doesn't exist
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1748): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `settings`
WHERE `key` = :key:', [...], false)
 2 APPPATH/Helpers/settings_helper.php(8): CodeIgniter\Database\BaseBuilder->getWhere([...])
 3 APPPATH/Helpers/settings_helper.php(65): get_settings('system_title')
 4 APPPATH/Views/Admin/_parts/header.php(6): get_site_title()
 5 APPPATH/Views/Admin/index.php(2): include_once('/home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php')
 6 SYSTEMPATH/View/View.php(228): include('/home/<USER>/apps/thredex/app/Views/Admin/index.php')
 7 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH/Common.php(1190): CodeIgniter\View\View->render('Admin/index', [], true)
 9 APPPATH/Controllers/Admin/Dashboard.php(18): view('Admin/index', [...])
10 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Admin\Dashboard->index()
11 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Dashboard))
12 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 18:00:07 --> App\Services\Shared\LoginService::authenticate(): Argument #1 ($email) must be of type string, null given, called in /home/<USER>/apps/thredex/app/Controllers/Login.php on line 58
in APPPATH/Services/Shared/LoginService.php on line 19.
 1 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate(null, null, false)
 2 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 3 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 18:01:18 --> App\Services\Shared\LoginService::authenticate(): Argument #2 ($password) must be of type string, null given, called in /home/<USER>/apps/thredex/app/Controllers/Login.php on line 58
in APPPATH/Services/Shared/LoginService.php on line 19.
 1 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('<EMAIL>', null, false)
 2 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 3 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
ERROR - 2025-06-18 18:04:33 --> mysqli_sql_exception: Table 'trogonapps_thredex.role' doesn't exist in /home/<USER>/apps/thredex/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /home/<USER>/apps/thredex/system/Database/MySQLi/Connection.php(306): mysqli->query('SELECT *\nFROM `...', 0)
#1 /home/<USER>/apps/thredex/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 /home/<USER>/apps/thredex/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 /home/<USER>/apps/thredex/system/Database/BaseBuilder.php(1615): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /home/<USER>/apps/thredex/app/Models/BaseModel.php(81): CodeIgniter\Database\BaseBuilder->get()
#5 /home/<USER>/apps/thredex/app/Services/Shared/LoginService.php(45): App\Models\BaseModel->get(Array)
#6 /home/<USER>/apps/thredex/app/Services/Shared/LoginService.php(35): App\Services\Shared\LoginService->generateUserdata(Array)
#7 /home/<USER>/apps/thredex/app/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('admin@thredex.c...', 'admin123', false)
#8 /home/<USER>/apps/thredex/app/Controllers/Login.php(35): App\Controllers\Login->processLogin()
#9 /home/<USER>/apps/thredex/system/CodeIgniter.php(942): App\Controllers\Login->admin_login()
#10 /home/<USER>/apps/thredex/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
#11 /home/<USER>/apps/thredex/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#12 /home/<USER>/public_html/thredex/index.php(83): CodeIgniter\CodeIgniter->run()
#13 {main}
CRITICAL - 2025-06-18 18:04:33 --> Table 'trogonapps_thredex.role' doesn't exist
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1615): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `role`
WHERE `id` = :id:
AND `role`.`deleted_at` IS NULL', [...], false)
 2 APPPATH/Models/BaseModel.php(81): CodeIgniter\Database\BaseBuilder->get()
 3 APPPATH/Services/Shared/LoginService.php(45): App\Models\BaseModel->get([...])
 4 APPPATH/Services/Shared/LoginService.php(35): App\Services\Shared\LoginService->generateUserdata([...])
 5 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('<EMAIL>', 'admin123', false)
 6 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 7 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 8 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 9 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 18:05:36 --> Undefined array key "user_id"
in APPPATH/Services/Shared/LoginService.php on line 47.
 1 APPPATH/Services/Shared/LoginService.php(47): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "user_id"', '/home/<USER>/apps/thredex/app/Services/Shared/LoginService.php', 47)
 2 APPPATH/Services/Shared/LoginService.php(35): App\Services\Shared\LoginService->generateUserdata([...])
 3 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('<EMAIL>', 'admin123', false)
 4 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 5 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 18:06:03 --> Undefined array key "user_id"
in APPPATH/Services/Shared/LoginService.php on line 47.
 1 APPPATH/Services/Shared/LoginService.php(47): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "user_id"', '/home/<USER>/apps/thredex/app/Services/Shared/LoginService.php', 47)
 2 APPPATH/Services/Shared/LoginService.php(35): App\Services\Shared\LoginService->generateUserdata([...])
 3 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('<EMAIL>', 'admin123', false)
 4 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 5 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 18:06:23 --> Undefined array key "name"
in APPPATH/Services/Shared/LoginService.php on line 50.
 1 APPPATH/Services/Shared/LoginService.php(50): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined array key "name"', '/home/<USER>/apps/thredex/app/Services/Shared/LoginService.php', 50)
 2 APPPATH/Services/Shared/LoginService.php(35): App\Services\Shared\LoginService->generateUserdata([...])
 3 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('<EMAIL>', 'admin123', false)
 4 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 5 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 6 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 7 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 18:06:43 --> App\Services\Shared\LoginService::generateUserdata(): Return value must be of type App\Services\Shared\Returntype, none returned
in APPPATH/Services/Shared/LoginService.php on line 55.
 1 APPPATH/Services/Shared/LoginService.php(35): App\Services\Shared\LoginService->generateUserdata([...])
 2 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('<EMAIL>', 'admin123', false)
 3 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 4 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 5 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 18:07:05 --> App\Services\Shared\LoginService::generateUserdata(): Return value must be of type App\Services\Shared\Returntype, array returned
in APPPATH/Services/Shared/LoginService.php on line 55.
 1 APPPATH/Services/Shared/LoginService.php(35): App\Services\Shared\LoginService->generateUserdata([...])
 2 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('<EMAIL>', 'admin123', false)
 3 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 4 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 5 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 18:10:15 --> App\Services\Shared\LoginService::generateUserdata(): Return value must be of type App\Services\Shared\Returntype, array returned
in APPPATH/Services/Shared/LoginService.php on line 55.
 1 APPPATH/Services/Shared/LoginService.php(35): App\Services\Shared\LoginService->generateUserdata([...])
 2 APPPATH/Controllers/Login.php(58): App\Services\Shared\LoginService->authenticate('<EMAIL>', 'admin123', false)
 3 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 4 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 5 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 22:12:31 --> Class "App\Controllers\PublicBaseController" not found
in APPPATH/Controllers/Login.php on line 7.
 1 SYSTEMPATH/Router/AutoRouter.php(161): include_once()
 2 SYSTEMPATH/Router/Router.php(514): CodeIgniter\Router\AutoRouter->getRoute('login', 'get')
 3 SYSTEMPATH/Router/Router.php(210): CodeIgniter\Router\Router->autoRoute('login')
 4 SYSTEMPATH/CodeIgniter.php(823): CodeIgniter\Router\Router->handle('login')
 5 SYSTEMPATH/CodeIgniter.php(450): CodeIgniter\CodeIgniter->tryToRouteIt(Object(CodeIgniter\Router\RouteCollection))
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 22:12:49 --> Undefined property: App\Controllers\Login::$data
in APPPATH/Controllers/Login.php on line 28.
 1 APPPATH/Controllers/Login.php(28): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined property: App\\Controllers\\Login::$data', '/home/<USER>/apps/thredex/app/Controllers/Login.php', 28)
 2 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->index()
 3 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 4 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 5 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
ERROR - 2025-06-18 22:23:13 --> mysqli_sql_exception: Table 'trogonapps_thredex.settings' doesn't exist in /home/<USER>/apps/thredex/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /home/<USER>/apps/thredex/system/Database/MySQLi/Connection.php(306): mysqli->query('SELECT *\nFROM `...', 0)
#1 /home/<USER>/apps/thredex/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 /home/<USER>/apps/thredex/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 /home/<USER>/apps/thredex/system/Database/BaseBuilder.php(1748): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /home/<USER>/apps/thredex/app/Helpers/settings_helper.php(8): CodeIgniter\Database\BaseBuilder->getWhere(Array)
#5 /home/<USER>/apps/thredex/app/Helpers/settings_helper.php(65): get_settings('system_title')
#6 /home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php(6): get_site_title()
#7 /home/<USER>/apps/thredex/app/Views/Admin/index.php(2): include_once('/home/<USER>')
#8 /home/<USER>/apps/thredex/system/View/View.php(228): include('/home/<USER>')
#9 /home/<USER>/apps/thredex/system/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#10 /home/<USER>/apps/thredex/system/Common.php(1190): CodeIgniter\View\View->render('Admin/index', Array, true)
#11 /home/<USER>/apps/thredex/app/Controllers/Admin/Dashboard.php(18): view('Admin/index', Array)
#12 /home/<USER>/apps/thredex/system/CodeIgniter.php(942): App\Controllers\Admin\Dashboard->index()
#13 /home/<USER>/apps/thredex/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Dashboard))
#14 /home/<USER>/apps/thredex/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#15 /home/<USER>/public_html/thredex/index.php(83): CodeIgniter\CodeIgniter->run()
#16 {main}
CRITICAL - 2025-06-18 22:23:13 --> Table 'trogonapps_thredex.settings' doesn't exist
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1748): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `settings`
WHERE `key` = :key:', [...], false)
 2 APPPATH/Helpers/settings_helper.php(8): CodeIgniter\Database\BaseBuilder->getWhere([...])
 3 APPPATH/Helpers/settings_helper.php(65): get_settings('system_title')
 4 APPPATH/Views/Admin/_parts/header.php(6): get_site_title()
 5 APPPATH/Views/Admin/index.php(2): include_once('/home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php')
 6 SYSTEMPATH/View/View.php(228): include('/home/<USER>/apps/thredex/app/Views/Admin/index.php')
 7 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH/Common.php(1190): CodeIgniter\View\View->render('Admin/index', [], true)
 9 APPPATH/Controllers/Admin/Dashboard.php(18): view('Admin/index', [...])
10 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Admin\Dashboard->index()
11 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Dashboard))
12 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 22:27:08 --> Undefined global variable $_SESSION
in APPPATH/Controllers/Login.php on line 63.
 1 APPPATH/Controllers/Login.php(63): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined global variable $_SESSION', '/home/<USER>/apps/thredex/app/Controllers/Login.php', 63)
 2 APPPATH/Controllers/Login.php(35): App\Controllers\Login->processLogin()
 3 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->admin_login()
 4 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 5 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 6 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
ERROR - 2025-06-18 22:29:30 --> mysqli_sql_exception: Table 'trogonapps_thredex.settings' doesn't exist in /home/<USER>/apps/thredex/system/Database/MySQLi/Connection.php:306
Stack trace:
#0 /home/<USER>/apps/thredex/system/Database/MySQLi/Connection.php(306): mysqli->query('SELECT *\nFROM `...', 0)
#1 /home/<USER>/apps/thredex/system/Database/BaseConnection.php(693): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 /home/<USER>/apps/thredex/system/Database/BaseConnection.php(607): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 /home/<USER>/apps/thredex/system/Database/BaseBuilder.php(1748): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /home/<USER>/apps/thredex/app/Helpers/settings_helper.php(8): CodeIgniter\Database\BaseBuilder->getWhere(Array)
#5 /home/<USER>/apps/thredex/app/Helpers/settings_helper.php(65): get_settings('system_title')
#6 /home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php(6): get_site_title()
#7 /home/<USER>/apps/thredex/app/Views/Admin/index.php(2): include_once('/home/<USER>')
#8 /home/<USER>/apps/thredex/system/View/View.php(228): include('/home/<USER>')
#9 /home/<USER>/apps/thredex/system/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
#10 /home/<USER>/apps/thredex/system/Common.php(1190): CodeIgniter\View\View->render('Admin/index', Array, true)
#11 /home/<USER>/apps/thredex/app/Controllers/Admin/Dashboard.php(18): view('Admin/index', Array)
#12 /home/<USER>/apps/thredex/system/CodeIgniter.php(942): App\Controllers\Admin\Dashboard->index()
#13 /home/<USER>/apps/thredex/system/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Dashboard))
#14 /home/<USER>/apps/thredex/system/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#15 /home/<USER>/public_html/thredex/index.php(83): CodeIgniter\CodeIgniter->run()
#16 {main}
CRITICAL - 2025-06-18 22:29:30 --> Table 'trogonapps_thredex.settings' doesn't exist
in SYSTEMPATH/Database/BaseConnection.php on line 647.
 1 SYSTEMPATH/Database/BaseBuilder.php(1748): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `settings`
WHERE `key` = :key:', [...], false)
 2 APPPATH/Helpers/settings_helper.php(8): CodeIgniter\Database\BaseBuilder->getWhere([...])
 3 APPPATH/Helpers/settings_helper.php(65): get_settings('system_title')
 4 APPPATH/Views/Admin/_parts/header.php(6): get_site_title()
 5 APPPATH/Views/Admin/index.php(2): include_once('/home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php')
 6 SYSTEMPATH/View/View.php(228): include('/home/<USER>/apps/thredex/app/Views/Admin/index.php')
 7 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 8 SYSTEMPATH/Common.php(1190): CodeIgniter\View\View->render('Admin/index', [], true)
 9 APPPATH/Controllers/Admin/Dashboard.php(18): view('Admin/index', [...])
10 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Admin\Dashboard->index()
11 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Dashboard))
12 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
13 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 22:31:45 --> Undefined variable $currentPage
in APPPATH/Views/Admin/_parts/navigation.php on line 29.
 1 APPPATH/Views/Admin/_parts/navigation.php(29): CodeIgniter\Debug\Exceptions->errorHandler(2, 'Undefined variable $currentPage', '/home/<USER>/apps/thredex/app/Views/Admin/_parts/navigation.php', 29)
 2 APPPATH/Views/Admin/_parts/header.php(13): include_once('/home/<USER>/apps/thredex/app/Views/Admin/_parts/navigation.php')
 3 APPPATH/Views/Admin/index.php(2): include_once('/home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php')
 4 SYSTEMPATH/View/View.php(228): include('/home/<USER>/apps/thredex/app/Views/Admin/index.php')
 5 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH/Common.php(1190): CodeIgniter\View\View->render('Admin/index', [], true)
 7 APPPATH/Controllers/Admin/Dashboard.php(18): view('Admin/index', [...])
 8 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Admin\Dashboard->index()
 9 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Dashboard))
10 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-18 22:34:00 --> include(templates/header.php): Failed to open stream: No such file or directory
in APPPATH/Views/Admin/Dashboard/index.php on line 368.
 1 APPPATH/Views/Admin/Dashboard/index.php(368): CodeIgniter\Debug\Exceptions->errorHandler(2, 'include(templates/header.php): Failed to open stream: No such file or directory', '/home/<USER>/apps/thredex/app/Views/Admin/Dashboard/index.php', 368)
 2 APPPATH/Views/Admin/Dashboard/index.php(368): include()
 3 APPPATH/Views/Admin/index.php(4): include_once('/home/<USER>/apps/thredex/app/Views/Admin/Dashboard/index.php')
 4 SYSTEMPATH/View/View.php(228): include('/home/<USER>/apps/thredex/app/Views/Admin/index.php')
 5 SYSTEMPATH/View/View.php(231): CodeIgniter\View\View->CodeIgniter\View\{closure}()
 6 SYSTEMPATH/Common.php(1190): CodeIgniter\View\View->render('Admin/index', [], true)
 7 APPPATH/Controllers/Admin/Dashboard.php(18): view('Admin/index', [...])
 8 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Admin\Dashboard->index()
 9 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Admin\Dashboard))
10 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
