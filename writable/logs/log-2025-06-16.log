CRITICAL - 2025-06-16 17:55:44 --> Class "App\Services\Shared\LoginService" not found
in APPPATH/Controllers/Login.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(914): App\Controllers\Login->__construct()
 2 SYSTEMPATH/CodeIgniter.php(493): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-16 17:56:31 --> Class "App\Services\Shared\LoginService" not found
in APPPATH/Controllers/Login.php on line 14.
 1 SYSTEMPATH/CodeIgniter.php(914): App\Controllers\Login->__construct()
 2 SYSTEMPATH/CodeIgniter.php(493): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-16 17:56:39 --> Invalid file: "Frontend/Login/index.php"
in SYSTEMPATH/Exceptions/FrameworkException.php on line 39.
 1 SYSTEMPATH/View/View.php(216): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('Frontend/Login/index.php')
 2 SYSTEMPATH/Common.php(1190): CodeIgniter\View\View->render('Frontend/Login/index', [], true)
 3 APPPATH/Controllers/Login.php(28): view('Frontend/Login/index', [])
 4 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->index()
 5 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
CRITICAL - 2025-06-16 18:07:48 --> Invalid file: "Frontend/Login/index.php"
in SYSTEMPATH/Exceptions/FrameworkException.php on line 39.
 1 SYSTEMPATH/View/View.php(216): CodeIgniter\Exceptions\FrameworkException::forInvalidFile('Frontend/Login/index.php')
 2 SYSTEMPATH/Common.php(1190): CodeIgniter\View\View->render('Frontend/Login/index', [], true)
 3 APPPATH/Controllers/Login.php(28): view('Frontend/Login/index', [])
 4 SYSTEMPATH/CodeIgniter.php(942): App\Controllers\Login->index()
 5 SYSTEMPATH/CodeIgniter.php(502): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Login))
 6 SYSTEMPATH/CodeIgniter.php(361): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 7 FCPATH/index.php(83): CodeIgniter\CodeIgniter->run()
