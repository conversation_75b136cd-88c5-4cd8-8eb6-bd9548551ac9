{"url": "https://trogonapps.com/thredex/admin/owners/add", "method": "GET", "isAJAX": false, "startTime": **********.5404, "totalTime": 24.***************, "totalMemory": "25.306", "segmentDuration": 5, "segmentCount": 5, "CI_VERSION": "4.4.3", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.543718, "duration": 0.008880853652954102}, {"name": "Routing", "component": "Timer", "start": **********.5526, "duration": 0.0005261898040771484}, {"name": "Before Filters", "component": "Timer", "start": **********.553739, "duration": 0.0027179718017578125}, {"name": "Controller", "component": "Timer", "start": **********.556458, "duration": 0.008235931396484375}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.556459, "duration": 0.003039121627807617}, {"name": "After Filters", "component": "Timer", "start": **********.564707, "duration": 7.700920104980469e-05}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(3 total Queries, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `key` = &#039;site_title&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1748", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Helpers/settings_helper.php:8", "function": "        CodeIgniter\\Database\\BaseBuilder->getWhere()", "index": "  2    "}, {"file": "APPPATH/Helpers/settings_helper.php:65", "function": "        get_settings()", "index": "  3    "}, {"file": "APPPATH/Views/Admin/_parts/header.php:6", "function": "        get_site_title()", "index": "  4    "}, {"file": "APPPATH/Views/Admin/index.php:2", "args": ["/home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php"], "function": "        include_once()", "index": "  5    "}, {"file": "SYSTEMPATH/View/View.php:228", "args": ["/home/<USER>/apps/thredex/app/Views/Admin/index.php"], "function": "        include()", "index": "  6    "}, {"file": "SYSTEMPATH/View/View.php:231", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  7    "}, {"file": "SYSTEMPATH/Common.php:1190", "function": "        CodeIgniter\\View\\View->render()", "index": "  8    "}, {"file": "APPPATH/Controllers/Admin/Owners.php:78", "function": "        view()", "index": "  9    "}, {"file": "SYSTEMPATH/CodeIgniter.php:942", "function": "        App\\Controllers\\Admin\\Owners->add()", "index": " 10    "}, {"file": "SYSTEMPATH/CodeIgniter.php:502", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 11    "}, {"file": "SYSTEMPATH/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 12    "}, {"file": "FCPATH/index.php:83", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 13    "}], "trace-file": "APPPATH/Helpers/settings_helper.php:8", "qid": "e1848fed660d2733b1d347f9ca162252"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.06 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `key` = &#039;site_title&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1748", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Helpers/settings_helper.php:8", "function": "        CodeIgniter\\Database\\BaseBuilder->getWhere()", "index": "  2    "}, {"file": "APPPATH/Helpers/settings_helper.php:65", "function": "        get_settings()", "index": "  3    "}, {"file": "APPPATH/Views/Admin/_parts/navigation.php:5", "function": "        get_site_title()", "index": "  4    "}, {"file": "APPPATH/Views/Admin/_parts/header.php:13", "args": ["/home/<USER>/apps/thredex/app/Views/Admin/_parts/navigation.php"], "function": "        include_once()", "index": "  5    "}, {"file": "APPPATH/Views/Admin/index.php:2", "args": ["/home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php"], "function": "        include_once()", "index": "  6    "}, {"file": "SYSTEMPATH/View/View.php:228", "args": ["/home/<USER>/apps/thredex/app/Views/Admin/index.php"], "function": "        include()", "index": "  7    "}, {"file": "SYSTEMPATH/View/View.php:231", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  8    "}, {"file": "SYSTEMPATH/Common.php:1190", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH/Controllers/Admin/Owners.php:78", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH/CodeIgniter.php:942", "function": "        App\\Controllers\\Admin\\Owners->add()", "index": " 11    "}, {"file": "SYSTEMPATH/CodeIgniter.php:502", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "FCPATH/index.php:83", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}], "trace-file": "APPPATH/Helpers/settings_helper.php:8", "qid": "c1d35add39ef56b1534252d73fa833d1"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.04 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `key` = &#039;site_title&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1748", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Helpers/settings_helper.php:8", "function": "        CodeIgniter\\Database\\BaseBuilder->getWhere()", "index": "  2    "}, {"file": "APPPATH/Helpers/settings_helper.php:65", "function": "        get_settings()", "index": "  3    "}, {"file": "APPPATH/Views/Admin/_parts/navigation.php:52", "function": "        get_site_title()", "index": "  4    "}, {"file": "APPPATH/Views/Admin/_parts/header.php:13", "args": ["/home/<USER>/apps/thredex/app/Views/Admin/_parts/navigation.php"], "function": "        include_once()", "index": "  5    "}, {"file": "APPPATH/Views/Admin/index.php:2", "args": ["/home/<USER>/apps/thredex/app/Views/Admin/_parts/header.php"], "function": "        include_once()", "index": "  6    "}, {"file": "SYSTEMPATH/View/View.php:228", "args": ["/home/<USER>/apps/thredex/app/Views/Admin/index.php"], "function": "        include()", "index": "  7    "}, {"file": "SYSTEMPATH/View/View.php:231", "function": "        CodeIgniter\\View\\View->CodeIgniter\\View\\{closure}", "index": "  8    "}, {"file": "SYSTEMPATH/Common.php:1190", "function": "        CodeIgniter\\View\\View->render()", "index": "  9    "}, {"file": "APPPATH/Controllers/Admin/Owners.php:78", "function": "        view()", "index": " 10    "}, {"file": "SYSTEMPATH/CodeIgniter.php:942", "function": "        App\\Controllers\\Admin\\Owners->add()", "index": " 11    "}, {"file": "SYSTEMPATH/CodeIgniter.php:502", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": " 12    "}, {"file": "SYSTEMPATH/CodeIgniter.php:361", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 13    "}, {"file": "FCPATH/index.php:83", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 14    "}], "trace-file": "APPPATH/Helpers/settings_helper.php:8", "qid": "8f2e9482a336e7acb7603fa17c9edf09"}]}, "badgeValue": 3, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.562566, "duration": "0.000566"}, {"name": "Query", "component": "Database", "start": **********.563412, "duration": "0.000204", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `key` = &#039;site_title&#039;"}, {"name": "Query", "component": "Database", "start": **********.564305, "duration": "0.000056", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `key` = &#039;site_title&#039;"}, {"name": "Query", "component": "Database", "start": **********.564453, "duration": "0.000035", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `key` = &#039;site_title&#039;"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 1, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: Admin/index.php", "component": "Views", "start": **********.560092, "duration": 0.004586935043334961}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 226 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/date_helper.php", "name": "date_helper.php"}, {"path": "SYSTEMPATH/Helpers/form_helper.php", "name": "form_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/AutoRouter.php", "name": "AutoRouter.php"}, {"path": "SYSTEMPATH/Router/AutoRouterInterface.php", "name": "AutoRouterInterface.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH/bootstrap.php", "name": "bootstrap.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/Admin/Owners.php", "name": "Owners.php"}, {"path": "APPPATH/Controllers/AdminBaseController.php", "name": "AdminBaseController.php"}, {"path": "APPPATH/Controllers/AppBaseController.php", "name": "AppBaseController.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Filters/AppAuthFilter.php", "name": "AppAuthFilter.php"}, {"path": "APPPATH/Helpers/ai_helper.php", "name": "ai_helper.php"}, {"path": "APPPATH/Helpers/app_helper.php", "name": "app_helper.php"}, {"path": "APPPATH/Helpers/countries_helper.php", "name": "countries_helper.php"}, {"path": "APPPATH/Helpers/database_helper.php", "name": "database_helper.php"}, {"path": "APPPATH/Helpers/date_helper.php", "name": "date_helper.php"}, {"path": "APPPATH/Helpers/jwt_helper.php", "name": "jwt_helper.php"}, {"path": "APPPATH/Helpers/login_helper.php", "name": "login_helper.php"}, {"path": "APPPATH/Helpers/mail_helper.php", "name": "mail_helper.php"}, {"path": "APPPATH/Helpers/navigation_helper.php", "name": "navigation_helper.php"}, {"path": "APPPATH/Helpers/permission_helper.php", "name": "permission_helper.php"}, {"path": "APPPATH/Helpers/settings_helper.php", "name": "settings_helper.php"}, {"path": "APPPATH/Helpers/site_helper.php", "name": "site_helper.php"}, {"path": "APPPATH/Helpers/time_helper.php", "name": "time_helper.php"}, {"path": "APPPATH/Helpers/ui_helper.php", "name": "ui_helper.php"}, {"path": "APPPATH/Helpers/upload_helper.php", "name": "upload_helper.php"}, {"path": "APPPATH/Helpers/user_helper.php", "name": "user_helper.php"}, {"path": "APPPATH/Helpers/vimeo_helper.php", "name": "vimeo_helper.php"}, {"path": "APPPATH/Models/BaseModel.php", "name": "BaseModel.php"}, {"path": "APPPATH/Models/OwnerProfileModel.php", "name": "OwnerProfileModel.php"}, {"path": "APPPATH/Models/RoleModel.php", "name": "RoleModel.php"}, {"path": "APPPATH/Models/UserModel.php", "name": "UserModel.php"}, {"path": "APPPATH/Services/Shared/OwnerService.php", "name": "OwnerService.php"}, {"path": "APPPATH/Views/Admin/Owners/add.php", "name": "add.php"}, {"path": "APPPATH/Views/Admin/_parts/footer.php", "name": "footer.php"}, {"path": "APPPATH/Views/Admin/_parts/header.php", "name": "header.php"}, {"path": "APPPATH/Views/Admin/_parts/header_includes.php", "name": "header_includes.php"}, {"path": "APPPATH/Views/Admin/_parts/navigation.php", "name": "navigation.php"}, {"path": "APPPATH/Views/Admin/index.php", "name": "index.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "VENDORPATH/autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH/aws/aws-sdk-php/src/functions.php", "name": "functions.php"}, {"path": "VENDORPATH/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH/composer/installed.php", "name": "installed.php"}, {"path": "VENDORPATH/composer/platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH/google/apiclient-services/autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH/google/apiclient/src/AccessToken/Revoke.php", "name": "Revoke.php"}, {"path": "VENDORPATH/google/apiclient/src/AccessToken/Verify.php", "name": "Verify.php"}, {"path": "VENDORPATH/google/apiclient/src/AuthHandler/AuthHandlerFactory.php", "name": "AuthHandlerFactory.php"}, {"path": "VENDORPATH/google/apiclient/src/AuthHandler/Guzzle6AuthHandler.php", "name": "Guzzle6AuthHandler.php"}, {"path": "VENDORPATH/google/apiclient/src/AuthHandler/Guzzle7AuthHandler.php", "name": "Guzzle7AuthHandler.php"}, {"path": "VENDORPATH/google/apiclient/src/Client.php", "name": "Client.php"}, {"path": "VENDORPATH/google/apiclient/src/Collection.php", "name": "Collection.php"}, {"path": "VENDORPATH/google/apiclient/src/Exception.php", "name": "Exception.php"}, {"path": "VENDORPATH/google/apiclient/src/Http/Batch.php", "name": "Batch.php"}, {"path": "VENDORPATH/google/apiclient/src/Http/MediaFileUpload.php", "name": "MediaFileUpload.php"}, {"path": "VENDORPATH/google/apiclient/src/Http/REST.php", "name": "REST.php"}, {"path": "VENDORPATH/google/apiclient/src/Model.php", "name": "Model.php"}, {"path": "VENDORPATH/google/apiclient/src/Service.php", "name": "Service.php"}, {"path": "VENDORPATH/google/apiclient/src/Service/Exception.php", "name": "Exception.php"}, {"path": "VENDORPATH/google/apiclient/src/Service/Resource.php", "name": "Resource.php"}, {"path": "VENDORPATH/google/apiclient/src/Task/Composer.php", "name": "Composer.php"}, {"path": "VENDORPATH/google/apiclient/src/Task/Exception.php", "name": "Exception.php"}, {"path": "VENDORPATH/google/apiclient/src/Task/Retryable.php", "name": "Retryable.php"}, {"path": "VENDORPATH/google/apiclient/src/Task/Runner.php", "name": "Runner.php"}, {"path": "VENDORPATH/google/apiclient/src/Utils/UriTemplate.php", "name": "UriTemplate.php"}, {"path": "VENDORPATH/google/apiclient/src/aliases.php", "name": "aliases.php"}, {"path": "VENDORPATH/guzzlehttp/guzzle/src/functions.php", "name": "functions.php"}, {"path": "VENDORPATH/guzzlehttp/guzzle/src/functions_include.php", "name": "functions_include.php"}, {"path": "VENDORPATH/kint-php/kint/init.php", "name": "init.php"}, {"path": "VENDORPATH/kint-php/kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "VENDORPATH/kint-php/kint/src/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "VENDORPATH/kint-php/kint/src/Kint.php", "name": "Kint.php"}, {"path": "VENDORPATH/kint-php/kint/src/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "VENDORPATH/kint-php/kint/src/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "VENDORPATH/kint-php/kint/src/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "VENDORPATH/kint-php/kint/src/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "VENDORPATH/kint-php/kint/src/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "VENDORPATH/kint-php/kint/src/Utils.php", "name": "Utils.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH/mpdf/mpdf/src/functions.php", "name": "functions.php"}, {"path": "VENDORPATH/mtdowling/jmespath.php/src/JmesPath.php", "name": "JmesPath.php"}, {"path": "VENDORPATH/myclabs/deep-copy/src/DeepCopy/deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH/phpseclib/phpseclib/phpseclib/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/phpunit/phpunit/src/Framework/Assert/Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH/psr/log/Psr/Log/LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH/psr/log/Psr/Log/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH/psr/log/Psr/Log/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH/ralouphie/getallheaders/src/getallheaders.php", "name": "getallheaders.php"}, {"path": "VENDORPATH/razorpay/razorpay/Deprecated.php", "name": "Deprecated.php"}, {"path": "VENDORPATH/rmccue/requests/library/Deprecated.php", "name": "Deprecated.php"}, {"path": "VENDORPATH/rmccue/requests/src/Autoload.php", "name": "Autoload.php"}, {"path": "VENDORPATH/symfony/deprecation-contracts/function.php", "name": "function.php"}, {"path": "VENDORPATH/symfony/polyfill-ctype/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/symfony/polyfill-ctype/bootstrap80.php", "name": "bootstrap80.php"}, {"path": "VENDORPATH/symfony/polyfill-iconv/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/symfony/polyfill-intl-grapheme/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/symfony/polyfill-intl-normalizer/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/symfony/polyfill-intl-normalizer/bootstrap80.php", "name": "bootstrap80.php"}, {"path": "VENDORPATH/symfony/polyfill-mbstring/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/symfony/polyfill-mbstring/bootstrap80.php", "name": "bootstrap80.php"}, {"path": "VENDORPATH/symfony/polyfill-php72/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/symfony/polyfill-php80/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/symfony/polyfill-php81/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/symfony/string/Resources/functions.php", "name": "functions.php"}, {"path": "VENDORPATH/voku/portable-utf8/bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH/voku/portable-utf8/src/voku/helper/Bootup.php", "name": "Bootup.php"}, {"path": "VENDORPATH/voku/portable-utf8/src/voku/helper/UTF8.php", "name": "UTF8.php"}]}, "badgeValue": 226, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "Admin/", "controller": "\\App\\Controllers\\Admin\\Owners", "method": "add", "paramCount": 0, "truePCount": 0, "params": []}], "routes": []}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "3.02", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.03", "count": 3}}}, "badgeValue": 4, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.548769, "duration": 0.0030219554901123047}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.563619, "duration": 1.621246337890625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.564363, "duration": 1.0013580322265625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.564489, "duration": 7.152557373046875e-06}]}], "vars": {"varData": {"View Data": {"page_title": "Add Owner", "page_name": "Owners/add"}}, "session": {"__ci_last_regenerate": "<pre>1750325421</pre>", "_ci_previous_url": "https://trogonapps.com/thredex/admin/owners/add", "user_id": "1", "role_id": "1", "user_email": "<EMAIL>", "user_name": "System Administrator", "logged_in": "<pre>1</pre>", "login_type": "web"}, "headers": {"X-Https": "1", "Cookie": "twk_uuid_6433d1634247f20fefeac44f=%7B%22uuid%22%3A%221.7xa1rLFYpQpYiNEXk934ZGPChRXA76exa9WuYZUwUqYNg8dTV8aYguJ7SshFNS1NSRTmd7CLMOKmnb9Ppu3xnvQCuj16LfjnhYAAbima51SUwmfoV1rxEtx5%22%2C%22version%22%3A3%2C%22domain%22%3A%22trogonapps.com%22%2C%22ts%22%3A1745735298586%7D; timezone=Asia/Kolkata; cpsession=trogonapps%3aAmwJRC2vUZDHMJlk%2cf551c566bd61dfea5aa0b358a6ee92f5; ci_session=b89f5a44346564ebc56200319109ae1dafc8c838", "Accept-Language": "en-US,en;q=0.9,ml;q=0.8,ar;q=0.7", "Accept-Encoding": "gzip, deflate, br, zstd", "Sec-Fetch-Dest": "document", "Sec-Fetch-User": "?1", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Upgrade-Insecure-Requests": "1", "Sec-Ch-Ua-Platform": "&quot;macOS&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua": "&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Connection": "keep-alive", "Host": "trogonapps.com"}, "cookies": {"twk_uuid_6433d1634247f20fefeac44f": "{&quot;uuid&quot;:&quot;1.7xa1rLFYpQpYiNEXk934ZGPChRXA76exa9WuYZUwUqYNg8dTV8aYguJ7SshFNS1NSRTmd7CLMOKmnb9Ppu3xnvQCuj16LfjnhYAAbima51SUwmfoV1rxEtx5&quot;,&quot;version&quot;:3,&quot;domain&quot;:&quot;trogonapps.com&quot;,&quot;ts&quot;:1745735298586}", "timezone": "Asia/Kolkata", "cpsession": "trogonapps:AmwJRC2vU<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,f551c566bd61dfea5aa0b358a6ee92f5", "ci_session": "b89f5a44346564ebc56200319109ae1dafc8c838"}, "request": "HTTPS/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.4.3", "phpVersion": "8.1.32", "phpSAPI": "fpm-fcgi", "environment": "development", "baseURL": "https://trogonapps.com/thredex/", "timezone": "Asia/Kolkata", "locale": "en", "cspEnabled": false}}