<?php
namespace App\Services\Admin;

use App\Models\UserModel;

class DashboardService
{
    protected $userModel;
    protected $roleModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    // get dashboard data
    public function getDashboardData(){
        $dashboardData = [
            'fact_cards' => $this->getFactCards(),
        ];
        return $dashboardData;
    }

    // get facts cards
    private function getFactCards(){
        // TODO: Change this later
        return [
            'owners' => 42,
            'buildings' => 45,
            'agents' => 45,
            'tenants' => 2678
        ];
    }



}
