<?php
namespace App\Services\Shared;

use App\Models\UserModel;
use App\Models\RoleModel;
use App\Models\OwnerProfileModel;

class OwnerService
{
    protected $userModel;
    protected $roleModel;
    protected $ownerProfileModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->roleModel = new RoleModel();
        $this->ownerProfileModel = new OwnerProfileModel();
    }

    public function createOwner($data)
    {
        $userData = [
            'role_id' => $data['role_id'] ?? null,
            'name' => $data['name'],
            'email' => $data['email'],
            'country_code' => $data['country_code'] ?? null,
            'phone' => $data['phone'],
            'password' => $data['password'],
            'profile_image' => $data['profile_image'] ?? null,
            'status' => 1
        ];

        $userResponse = $this->userModel->insert($userData);
        if (!$userResponse) {
            return ['success' => false, 'message' => 'Failed to create user'];
        }

        $userId = $userResponse;
        $ownerData = [
            'user_id' => $userId,
            'phone_secondary' => $data['phone_secondary'] ?? null,
            'business_name' => $data['business_name'] ?? null,
            'tax_number' => $data['tax_number'] ?? null,
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'country' => $data['country'] ?? null,
            'post_code' => $data['post_code'] ?? null,
        ];

        $ownerResponse = $this->ownerProfileModel->insert($ownerData);
        if (!$ownerResponse) {
            // Rollback user creation if owner profile fails
            $this->userModel->delete($userId);
            return ['success' => false, 'message' => 'Failed to create owner profile'];
        }

        return ['success' => true, 'message' => 'Owner created successfully', 'user_id' => $userId];
    }

    public function getList($limit = null, $offset = 0, $search = null)
    {
        $builder = $this->userModel->db->table('users u');
        $builder->select('u.*, op.phone_secondary, op.business_name, op.tax_number, op.address, op.city, op.state, op.country, op.post_code, r.title as role_title');
        $builder->join('owner_profiles op', 'op.user_id = u.id', 'left');
        $builder->join('roles r', 'r.id = u.role_id', 'left');
        $builder->where('u.deleted_at', null);

        if ($search) {
            $builder->groupStart();
            $builder->like('u.name', $search);
            $builder->orLike('u.email', $search);
            $builder->orLike('u.phone', $search);
            $builder->orLike('op.business_name', $search);
            $builder->groupEnd();
        }

        $builder->orderBy('u.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit, $offset);
        }

        return $builder->get()->getResultArray();
    }

    public function getById($id)
    {
        $builder = $this->userModel->db->table('users u');
        $builder->select('u.*, op.phone_secondary, op.business_name, op.tax_number, op.address, op.city, op.state, op.country, op.post_code, r.title as role_title');
        $builder->join('owner_profiles op', 'op.user_id = u.id', 'left');
        $builder->join('roles r', 'r.id = u.role_id', 'left');
        $builder->where('u.id', $id);
        $builder->where('u.deleted_at', null);

        return $builder->get()->getRowArray();
    }

    public function updateOwner($id, $data)
    {
        $userData = [
            'name' => $data['name'],
            'email' => $data['email'],
            'country_code' => $data['country_code'] ?? null,
            'phone' => $data['phone'],
            'status' => $data['status'] ?? 1
        ];

        if (isset($data['password']) && !empty($data['password'])) {
            $userData['password'] = $data['password'];
        }

        if (isset($data['profile_image'])) {
            $userData['profile_image'] = $data['profile_image'];
        }

        $userResponse = $this->userModel->update($id, $userData);
        if (!$userResponse) {
            return ['success' => false, 'message' => 'Failed to update user'];
        }

        $ownerData = [
            'phone_secondary' => $data['phone_secondary'] ?? null,
            'business_name' => $data['business_name'] ?? null,
            'tax_number' => $data['tax_number'] ?? null,
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'country' => $data['country'] ?? null,
            'post_code' => $data['post_code'] ?? null,
        ];

        // Check if owner profile exists
        $existingProfile = $this->ownerProfileModel->where('user_id', $id)->first();

        if ($existingProfile) {
            $ownerResponse = $this->ownerProfileModel->update($existingProfile->id, $ownerData);
        } else {
            $ownerData['user_id'] = $id;
            $ownerResponse = $this->ownerProfileModel->insert($ownerData);
        }

        if (!$ownerResponse) {
            return ['success' => false, 'message' => 'Failed to update owner profile'];
        }

        return ['success' => true, 'message' => 'Owner updated successfully'];
    }

    public function deleteOwner($id)
    {
        // Soft delete the user (which will cascade to owner profile due to foreign key)
        $userResponse = $this->userModel->delete($id);
        if (!$userResponse) {
            return ['success' => false, 'message' => 'Failed to delete owner'];
        }

        // Also soft delete the owner profile
        $ownerProfile = $this->ownerProfileModel->where('user_id', $id)->first();
        if ($ownerProfile) {
            $this->ownerProfileModel->delete($ownerProfile->id);
        }

        return ['success' => true, 'message' => 'Owner deleted successfully'];
    }

    public function getTotalCount($search = null)
    {
        $builder = $this->userModel->db->table('users u');
        $builder->join('owner_profiles op', 'op.user_id = u.id', 'left');
        $builder->where('u.deleted_at', null);

        if ($search) {
            $builder->groupStart();
            $builder->like('u.name', $search);
            $builder->orLike('u.email', $search);
            $builder->orLike('u.phone', $search);
            $builder->orLike('op.business_name', $search);
            $builder->groupEnd();
        }

        return $builder->countAllResults();
    }
}
