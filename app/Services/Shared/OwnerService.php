<?php
namespace App\Services\Shared;

use App\Models\UserModel;
use App\Models\RoleModel;
use App\Models\OwnerProfileModel;

class OwnerService
{
    protected $userModel;
    protected $roleModel;
    protected $ownerProfileModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->roleModel = new RoleModel();
        $this->ownerProfileModel = new OwnerProfileModel();
    }

    public function createOwner($data)
    {
        $userData = [
            'role_id' => $data['role_id'],
            'name' => $data['name'],
            'email' => $data['email'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'password' => $data['password'],
            'profile_image' => $data['profile_image']
        ];
        $userResponse = $this->userModel->insert($userData);
        if (!$userResponse) {
            return ['success' => false, 'message' => $userResponse];
        }
        $userId = $userResponse;
        $ownerData = [
            'user_id' => $userId,
            'phone_secondary' => $data['phone_secondary'],
            'business_name' => $data['business_name'],
            'tax_number' => $data['tax_number'],
            'address' => $data['address'],
            'city' => $data['city'],
            'state' => $data['state'],
            'country' => $data['country'],
            'post_code' => $data['post_code'],
        ];
        $ownerResponse = $this->ownerProfileModel->insert($ownerData);
        if (!$ownerResponse) {
            return ['success' => false, 'message' => $ownerResponse];
        }
        $ownerId = $ownerResponse;
        return ['success' => true, 'message' => $ownerId];
    }
}
