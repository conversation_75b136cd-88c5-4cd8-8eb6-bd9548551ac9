<?php
namespace App\Services\Shared;

use App\Models\UserModel;
use App\Models\RoleModel;
use App\Models\OwnerProfileModel;

class OwnerService
{
    protected $userModel;
    protected $roleModel;
    protected $ownerProfileModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->roleModel = new RoleModel();
        $this->ownerProfileModel = new OwnerProfileModel();
    }

    public function createOwner($data)
    {
        $userData = [
            'role_id' => 2, // Owner role
            'name' => $data['name'],
            'email' => $data['email'],
            'country_code' => $data['country_code'] ?? null,
            'phone' => $data['phone'],
            'password' => $data['password'],
            'profile_image' => $data['profile_image'] ?? null,
            'status' => 1
        ];

        $userResponse = $this->userModel->insert($userData);
        if (!$userResponse) {
            return ['success' => false, 'message' => 'Failed to create user'];
        }

        $userId = $userResponse;
        $ownerData = [
            'user_id' => $userId,
            'phone_secondary' => $data['phone_secondary'] ?? null,
            'business_name' => $data['business_name'] ?? null,
            'tax_number' => $data['tax_number'] ?? null,
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'country' => $data['country'] ?? null,
            'post_code' => $data['post_code'] ?? null,
        ];

        $ownerResponse = $this->ownerProfileModel->insert($ownerData);
        if (!$ownerResponse) {
            // Rollback user creation if owner profile fails
            $this->userModel->delete($userId);
            return ['success' => false, 'message' => 'Failed to create owner profile'];
        }

        return ['success' => true, 'message' => 'Owner created successfully', 'user_id' => $userId];
    }

    public function getList($limit = null, $offset = 0, $filters = [])
    {
        $builder = $this->userModel->db->table('users u');
        $builder->select('u.*, op.phone_secondary, op.business_name, op.tax_number, op.address, op.city, op.state, op.country, op.post_code, r.title as role_title');
        $builder->join('owner_profiles op', 'op.user_id = u.id', 'left');
        $builder->join('roles r', 'r.id = u.role_id', 'left');
        $builder->where('u.deleted_at', null);
        $builder->where('u.role_id', 2); // Only owners

        // Search filter
        if (!empty($filters['search'])) {
            $builder->groupStart();
            $builder->like('u.name', $filters['search']);
            $builder->orLike('u.email', $filters['search']);
            $builder->orLike('u.phone', $filters['search']);
            $builder->orLike('op.business_name', $filters['search']);
            $builder->groupEnd();
        }

        // Status filter
        if (isset($filters['status']) && $filters['status'] !== '') {
            $builder->where('u.status', $filters['status']);
        }

        // Is blocked filter
        if (isset($filters['is_blocked']) && $filters['is_blocked'] !== '') {
            $builder->where('u.is_blocked', $filters['is_blocked']);
        }

        // Date range filter
        if (!empty($filters['date_from'])) {
            $builder->where('DATE(u.created_at) >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('DATE(u.created_at) <=', $filters['date_to']);
        }

        $builder->orderBy('u.created_at', 'DESC');

        if ($limit) {
            $builder->limit($limit, $offset);
        }

        return $builder->get()->getResultArray();
    }

    public function getById($id)
    {
        $builder = $this->userModel->db->table('users u');
        $builder->select('u.*, op.phone_secondary, op.business_name, op.tax_number, op.address, op.city, op.state, op.country, op.post_code, r.title as role_title');
        $builder->join('owner_profiles op', 'op.user_id = u.id', 'left');
        $builder->join('roles r', 'r.id = u.role_id', 'left');
        $builder->where('u.id', $id);
        $builder->where('u.role_id', 2); // Only owners
        $builder->where('u.deleted_at', null);

        return $builder->get()->getRowArray();
    }

    public function updateOwner($id, $data)
    {
        $userData = [
            'role_id' => 2, // Ensure owner role
            'name' => $data['name'],
            'email' => $data['email'],
            'country_code' => $data['country_code'] ?? null,
            'phone' => $data['phone'],
            'status' => $data['status'] ?? 1,
            'is_blocked' => $data['is_blocked'] ?? 0
        ];

        if (isset($data['password']) && !empty($data['password'])) {
            $userData['password'] = $data['password'];
        }

        if (isset($data['profile_image'])) {
            $userData['profile_image'] = $data['profile_image'];
        }

        $userResponse = $this->userModel->update($id, $userData);
        if (!$userResponse) {
            return ['success' => false, 'message' => 'Failed to update user'];
        }

        $ownerData = [
            'phone_secondary' => $data['phone_secondary'] ?? null,
            'business_name' => $data['business_name'] ?? null,
            'tax_number' => $data['tax_number'] ?? null,
            'address' => $data['address'] ?? null,
            'city' => $data['city'] ?? null,
            'state' => $data['state'] ?? null,
            'country' => $data['country'] ?? null,
            'post_code' => $data['post_code'] ?? null,
        ];

        // Check if owner profile exists
        $existingProfile = $this->ownerProfileModel->where('user_id', $id)->first();

        if ($existingProfile) {
            $ownerResponse = $this->ownerProfileModel->update($existingProfile->id, $ownerData);
        } else {
            $ownerData['user_id'] = $id;
            $ownerResponse = $this->ownerProfileModel->insert($ownerData);
        }

        if (!$ownerResponse) {
            return ['success' => false, 'message' => 'Failed to update owner profile'];
        }

        return ['success' => true, 'message' => 'Owner updated successfully'];
    }

    public function deleteOwner($id)
    {
        // Soft delete the user (which will cascade to owner profile due to foreign key)
        $userResponse = $this->userModel->delete($id);
        if (!$userResponse) {
            return ['success' => false, 'message' => 'Failed to delete owner'];
        }

        // Also soft delete the owner profile
        $ownerProfile = $this->ownerProfileModel->where('user_id', $id)->first();
        if ($ownerProfile) {
            $this->ownerProfileModel->delete($ownerProfile->id);
        }

        return ['success' => true, 'message' => 'Owner deleted successfully'];
    }

    public function getTotalCount($filters = [])
    {
        $builder = $this->userModel->db->table('users u');
        $builder->join('owner_profiles op', 'op.user_id = u.id', 'left');
        $builder->where('u.deleted_at', null);
        $builder->where('u.role_id', 2); // Only owners

        // Search filter
        if (!empty($filters['search'])) {
            $builder->groupStart();
            $builder->like('u.name', $filters['search']);
            $builder->orLike('u.email', $filters['search']);
            $builder->orLike('u.phone', $filters['search']);
            $builder->orLike('op.business_name', $filters['search']);
            $builder->groupEnd();
        }

        // Status filter
        if (isset($filters['status']) && $filters['status'] !== '') {
            $builder->where('u.status', $filters['status']);
        }

        // Is blocked filter
        if (isset($filters['is_blocked']) && $filters['is_blocked'] !== '') {
            $builder->where('u.is_blocked', $filters['is_blocked']);
        }

        // Date range filter
        if (!empty($filters['date_from'])) {
            $builder->where('DATE(u.created_at) >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('DATE(u.created_at) <=', $filters['date_to']);
        }

        return $builder->countAllResults();
    }
}
