<?php
namespace App\Services\Shared;

use App\Models\UserModel;
use App\Models\RoleModel;

class LoginService
{
    protected $userModel;
    protected $roleModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->roleModel = new RoleModel();
    }

    // authenticate user
    public function authenticate(string $email, string $password, $isWeb = true)
    {
        $user = $this->userModel->get(['email' => $email, 'status' => 1])->getRowArray();

        if (!$user) {
            return ['success' => false, 'message' => 'User not found or inactive'];
        }

        if (!password_verify($password, $user['password'])) {
            return ['success' => false, 'message' => 'Invalid password'];
        }

        // Optional custom checks
        if ($user['is_blocked']) {
            return ['success' => false, 'message' => 'Account is blocked'];
        }
        $userData = $this->generateUserdata($user);

        if($isWeb){
            $this->setUserSession($userData);
        }
        return ['success' => true, 'user' => $userData];
    }

    // geenrate user data on login
    private function generateUserdata($user) {
        $roleTitle = $this->roleModel->get(['id' => $user['role_id']])->getRow()->title;
        $userData = [
            'user_id' => $user['id'],
            'role_id' => $user['role_id'],
            'role_title' => $roleTitle,
            'name' => $user['name'],
            'email' => $user['email'],
            'country_code' => $user['country_code'],
            'phone' => $user['phone']
        ];
        return $userData;
    }

    // generate session data for web login
    private function setUserSession($user) {
        session()->set([
            'user_id'     => $user['user_id'],
            'role_id'     => $user['role_id'],
            'user_email'  => $user['email'],
            'user_name'   => $user['name'] ?? null,
            'logged_in'   => true,
            'login_type'  => 'web', // or 'api' if needed later
        ]);
    }

    // logout the user
    public function logout() {
        session()->destroy();
    }


}
