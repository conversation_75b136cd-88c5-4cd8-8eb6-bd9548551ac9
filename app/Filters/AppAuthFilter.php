<?php
namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class AppAuthFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // $public_urls = ['app/home/<USER>'];
        
        // if (!in_array($request->getPath(), $public_urls))
        // {
            // Check if the URL starts with 'app/'
            if (str_starts_with($request->getPath(), 'admin/')) {
                return check_login(1);
            }
            if (str_starts_with($request->getPath(), 'owner/')) {
                return check_login(2);
            }
            if (str_starts_with($request->getPath(), 'agent/')) {
                return check_login(3);
            }
            if (str_starts_with($request->getPath(), 'tenant/')) {
                return check_login(4);
            }
        // }
        
        
        
        
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Usually, nothing is needed here for an auth check
    }
}
