<?php namespace App\Models;

use CodeIgniter\Model;

class UserModel extends BaseModel
{
    protected $table         = 'users';      // Database table name
    protected $primaryKey    = 'id';         // Primary key of the table
    protected $returnType    = 'App\Entities\User';  // Entity class name
    protected $useTimestamps = true;         // Auto handle timestamps
    protected $allowedFields = ['role_id', 'name', 'email', 'country_code', 'phone', 'password', 'status', 'is_blocked', 'last_login'];  // Fields that can be manipulated

    protected $beforeInsert = ['password_hash_model'];
    protected $beforeUpdate = ['password_hash_model'];

    protected function password_hash_model(array $data)
    {
        if (isset($data['password'])) {
            $data['password'] = $this->password_hash($data['password']);
        }
        return $data;
    }

}
