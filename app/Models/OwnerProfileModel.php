<?php namespace App\Models;

use App\Entities\OwnerProfile;
use CodeIgniter\Model;

class OwnerProfileModel extends BaseModel
{
    protected $table            = 'owner_profiles';
    protected $primaryKey       = 'id';
    protected $returnType       = 'App\Entities\OwnerProfile';
    protected $useTimestamps    = true;

    protected $allowedFields = [
        'user_id',
        'phone_secondary',
        'business_name',
        'tax_number',
        'address',
        'city',
        'country',
        'post_code'
    ];

    protected $validationRules = [
        'user_id'         => 'required|is_natural_no_zero',
        'business_name'   => 'permit_empty|max_length[200]',
        'tax_number'      => 'permit_empty|max_length[30]',
        'address'         => 'permit_empty|max_length[200]',
        'phone_secondary' => 'permit_empty|max_length[20]',
        'city'            => 'permit_empty|max_length[100]',
        'country'         => 'permit_empty|max_length[100]',
        'post_code'       => 'permit_empty|max_length[20]',
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'is_natural_no_zero' => 'Invalid User ID'
        ]
    ];
}
