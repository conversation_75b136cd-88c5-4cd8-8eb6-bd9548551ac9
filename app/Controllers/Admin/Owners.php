<?php
namespace App\Controllers\Admin;

use App\Services\Shared\OwnerService;
use App\Controllers\AdminBaseController;

class Owners extends AdminBaseController
{
    private $ownerService;

    public function __construct()
    {
        parent::__construct();
        $this->ownerService = new OwnerService();
    }

    public function index()
    {
        $filters = [
            'search' => $this->request->getGet('search'),
            'status' => $this->request->getGet('status'),
            'is_blocked' => $this->request->getGet('is_blocked'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to')
        ];

        $page = $this->request->getGet('page') ?? 1;
        $limit = 10;
        $offset = ($page - 1) * $limit;

        $this->data['list_items'] = $this->ownerService->getList($limit, $offset, $filters);
        $this->data['total_count'] = $this->ownerService->getTotalCount($filters);
        $this->data['current_page'] = $page;
        $this->data['total_pages'] = ceil($this->data['total_count'] / $limit);
        $this->data['filters'] = $filters;

        // If AJAX request, return only the content
        if ($this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => true,
                'html' => view('Admin/Owners/ajax_list', $this->data),
                'pagination' => view('Admin/Owners/ajax_pagination', $this->data),
                'total_count' => $this->data['total_count']
            ]);
        }

        $this->data['page_title'] = 'Owners Management';
        $this->data['page_name'] = 'Owners/index';
        return view('Admin/index', $this->data);
    }

    public function add()
    {
        if($this->request->getMethod() === 'post')
        {
            $validation = \Config\Services::validation();
            $validation->setRules($this->ownerValidationRules(), $this->ownerValidationMessages());

            if (!$validation->withRequest($this->request)->run()) {
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Please fix the validation errors',
                        'errors' => $validation->getErrors()
                    ]);
                }
                session()->setFlashdata('message_danger', 'Please fix the validation errors');
                session()->setFlashdata('validation_errors', $validation->getErrors());
                return redirect()->back()->withInput();
            }

            $userData = [
                'name' => $this->request->getPost('name'),
                'email' => $this->request->getPost('email'),
                'country_code' => $this->request->getPost('country_code'),
                'phone' => $this->request->getPost('phone'),
                'password' => $this->request->getPost('password'),
                'phone_secondary' => $this->request->getPost('phone_secondary'),
                'business_name' => $this->request->getPost('business_name'),
                'tax_number' => $this->request->getPost('tax_number'),
                'address' => $this->request->getPost('address'),
                'city' => $this->request->getPost('city'),
                'state' => $this->request->getPost('state'),
                'country' => $this->request->getPost('country'),
                'post_code' => $this->request->getPost('post_code'),
            ];

            $image = $this->upload_file_public('profile_image', 'profile_image');
            if($image && !empty($image['file'])){
                $userData['profile_image'] = $image['file'];
            }

            $ownerData = $this->ownerService->createOwner($userData);

            if ($ownerData['success']) {
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON([
                        'success' => true,
                        'message' => 'Owner added successfully!',
                        'redirect' => base_url('admin/owners')
                    ]);
                }
                session()->setFlashdata('message_success', 'Owner added successfully!');
                return redirect()->to('admin/owners');
            } else {
                if ($this->request->isAJAX()) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => $ownerData['message']
                    ]);
                }
                session()->setFlashdata('message_danger', $ownerData['message']);
                return redirect()->back()->withInput();
            }
        }

        $this->data['page_title'] = 'Add Owner';
        $this->data['page_name'] = 'Owners/add';
        return view('Admin/index', $this->data);
    }

    private function ownerValidationRules(): array
    {
        return [
            'name'         => 'required|min_length[4]',
            'email'        => 'required|valid_email|is_unique_soft[users.email]',
            'country_code' => 'required',
            'phone'        => 'required|is_unique_soft[users.phone]',
            'password'     => 'required|min_length[8]',
        ];
    }

    private function ownerValidationMessages(): array
    {
        return [
            'email' => [
                'is_unique_soft' => 'This email address is already registered.'
            ],
            'phone' => [
                'is_unique_soft' => 'This phone number is already registered.'
            ],
            'name' => [
                'required'    => 'Name is required.',
                'min_length'  => 'Name must be at least 4 characters.',
            ],
            'password' => [
                'required'    => 'Password is required.',
                'min_length'  => 'Password must be at least 8 characters.',
            ],
        ];
    }
    
    public function edit($id)
    {
        if($this->request->getMethod() === 'post')
        {
            $validation = \Config\Services::validation();
            $validation->setRules($this->ownerValidationRulesForEdit($id), $this->ownerValidationMessages());

            if (!$validation->withRequest($this->request)->run()) {
                session()->setFlashdata('message_danger', 'Please fix the validation errors');
                session()->setFlashdata('validation_errors', $validation->getErrors());
                return redirect()->back()->withInput();
            }

            $userData = [
                'name' => $this->request->getPost('name'),
                'email' => $this->request->getPost('email'),
                'country_code' => $this->request->getPost('country_code'),
                'phone' => $this->request->getPost('phone'),
                'phone_secondary' => $this->request->getPost('phone_secondary'),
                'business_name' => $this->request->getPost('business_name'),
                'tax_number' => $this->request->getPost('tax_number'),
                'address' => $this->request->getPost('address'),
                'city' => $this->request->getPost('city'),
                'state' => $this->request->getPost('state'),
                'country' => $this->request->getPost('country'),
                'post_code' => $this->request->getPost('post_code'),
                'status' => $this->request->getPost('status') ?? 1,
                'is_blocked' => $this->request->getPost('is_blocked') ?? 0,
            ];

            // Only update password if provided
            $password = $this->request->getPost('password');
            if (!empty($password)) {
                $userData['password'] = $password;
            }

            $image = $this->upload_file_public('profile_image', 'profile_image');
            if($image && !empty($image['file'])){
                $userData['profile_image'] = $image['file'];
            }

            $response = $this->ownerService->updateOwner($id, $userData);

            if ($response['success']) {
                session()->setFlashdata('message_success', 'Owner updated successfully!');
                return redirect()->to('admin/owners');
            } else {
                session()->setFlashdata('message_danger', $response['message']);
                return redirect()->back()->withInput();
            }
        }

        $this->data['edit_data'] = $this->ownerService->getById($id);
        if (!$this->data['edit_data']) {
            session()->setFlashdata('message_danger', 'Owner not found');
            return redirect()->to('admin/owners');
        }

        $this->data['page_title'] = 'Edit Owner';
        $this->data['page_name'] = 'Owners/edit';
        return view('Admin/index', $this->data);
    }

    public function delete($id)
    {
        if($id > 0)
        {
            $response = $this->ownerService->deleteOwner($id);

            if ($response['success']) {
                session()->setFlashdata('message_success', 'Owner deleted successfully!');
            } else {
                session()->setFlashdata('message_danger', $response['message']);
            }
        }
        return redirect()->back();
    }

    private function ownerValidationRulesForEdit($id): array
    {
        return [
            'name'         => 'required|min_length[4]',
            'email'        => "required|valid_email|is_unique_soft[users.email,id,{$id}]",
            'country_code' => 'required',
            'phone'        => "required|is_unique_soft[users.phone,id,{$id}]",
            'password'     => 'permit_empty|min_length[8]',
        ];
    }
}