<?php
namespace App\Controllers\Admin;

use App\Services\Shared\OwnerService;
use App\Controllers\AdminBaseController;

class Owners extends AdminBaseController
{
    private $ownerService;
    
    public function __construct()
    {
        parent::__construct();
        $this->ownerService = new OwnerService();
    }

    public function index()
    {
        $this->data['list_items'] = $this->ownerService->getList();
        
        $this->data['page_title'] = 'Owners List';
        $this->data['page_name'] = 'Owners/index';
        return view('Admin/index', $this->data);
    }

    public function add()
    {
        
        if ($this->request->isAJAX()) {
            if (!$this->validate($this->ownerValidationRules(), $this->ownerValidationMessages())) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors'  => $this->validator->getErrors(),
                ]);
            }
            
            $userData = [
                'name' => $this->request->getPost('name'),
                'email' => $this->request->getPost('email'),
                'country_code' => $this->request->getPost('country_code'),
                'phone' => $this->request->getPost('phone'),
                'password' => $this->request->getPost('password'),
                'phone_secondary' => $this->request->getPost('phone_secondary'),
                'business_name' => $this->request->getPost('business_name'),
                'tax_number' => $this->request->getPost('tax_number'),
                'address' => $this->request->getPost('address'),
                'city' => $this->request->getPost('city'),
                'state' => $this->request->getPost('state'),
                'country' => $this->request->getPost('country'),
                'post_code' => $this->request->getPost('post_code'),
            ];
            
            $image = $this->upload_file_public('profile_image', 'profile_image');

            if($image && !empty($image['file'])){
                $userData['profile_image'] = $image['file'];
            }
            
            $ownerData = $this->ownerService->createOwner($userData);
            
            if ($ownerData['success']) {
                $this->respond([
                    'success' => true,
                    'message' => 'Added Successfully!',
                    'data' => []
                ], 'admin/owners');
            } else {
                $this->respond([
                    'success' => false,
                    'message' => 'Something went wrong! Try Again',
                ], 'admin/owners');
            }
            return redirect()->back();
        }
        $this->data['page_title'] = 'Add Owner';
        $this->data['page_name'] = 'Owners/add';
        return view('Admin/index', $this->data);
    }

    private function ownerValidationRules(): array
    {
        return [
            'name'         => 'required|min_length[4]',
            'email'        => 'required|valid_email|is_unique_soft[users.email]',
            'country_code' => 'required',
            'phone'        => 'required|is_unique_soft[users.phone]',
            'password'     => 'required|min_length[8]',
        ];
    }

    private function ownerValidationMessages(): array
    {
        return [
            'email' => [
                'is_unique_soft' => 'This email address is already registered.'
            ],
            'phone' => [
                'is_unique_soft' => 'This phone number is already registered.'
            ],
            'name' => [
                'required'    => 'Name is required.',
                'min_length'  => 'Name must be at least 4 characters.',
            ],
            'password' => [
                'required'    => 'Password is required.',
                'min_length'  => 'Password must be at least 8 characters.',
            ],
        ];
    }
    
    public function ajax_edit($id){
        $this->data['item_id'] = $id;
        $this->data['edit_data'] = $this->ownerService->get(['id' => $id])->getRowArray();
        echo view('Admin/Banner/ajax_edit', $this->data);
    }
    
    public function edit($item_id)
    {
        if($this->request->getMethod() === 'post')
        {
            // $data = [
            //     'title' => $this->request->getPost('title'),
            // ];
            
            $image = $this->upload_file_public('area', 'image');
            if($image && !empty($image['file']))
            {
                $data['thumbnail'] = $image['file'];
            }
            
            $response = $this->banner_model->edit($data, ['id' => $item_id]);
            
            if ($response) {
                session()->setFlashdata('message_success', 'Updated Successfully!');
            } else {
                session()->setFlashdata('message_danger', 'Something went wrong! Try Again');
            }
        }
        return redirect()->back();
    }

    public function delete($item_id)
    {
        if($item_id > 0)
        {
            if ($this->banner_model->remove(['id' => $item_id])) {
                session()->setFlashdata('message_success', ' Deleted Successfully!');
            } else {
                session()->setFlashdata('message_danger', 'Something went wrong! Try Again');
            }
        }
        return redirect()->back();
    }
}