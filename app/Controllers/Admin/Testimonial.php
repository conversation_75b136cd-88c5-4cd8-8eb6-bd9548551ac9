<?php
namespace App\Controllers\Admin;
use App\Models\Testimonial_model;

class Testimonial extends AdminBaseController
{
    private $banner_model;
    public function __construct()
    {
        parent::__construct();
        $this->testimonial_model = new Testimonial_model();
    }

    public function index(){
        
        $this->data['list_items'] = $this->testimonial_model->get()->getResultArray();
        
        $this->data['page_title'] = 'Testimonial';
        $this->data['page_name'] = 'Testimonial/index';
        return view('Admin/index', $this->data);
    }
    
    public function ajax_add(){
        echo view('Admin/Testimonial/ajax_add', $this->data);
    }

    public function add(){
        if($this->request->getMethod() === 'post'){
        $data = [
                    'name'        => $this->request->getPost('name'),
                    'designation' => $this->request->getPost('designation'),
                    'date'        => $this->request->getPost('date'),
                    'review'      => $this->request->getPost('review'),
                    'rating'      => $this->request->getPost('rating'),
                    'created_at'  => date('Y-m-d H:i:s'),
                    'created_by'  => get_user_id(),
                ];              
                
                $image = $this->upload_file('testimonial','testimonial');
                if($image && valid_file($image['file'])){
    				$data['photo'] = $image['file'];
    			}
			
			
			
            $cat_id = $this->testimonial_model->add($data);
            if ($cat_id){
                session()->setFlashdata('message_success', "testimonial Added Successfully!");
            }else{
                session()->setFlashdata('message_danger', "Something went wrong! Try Again");
            }
        }
        return redirect()->to(base_url('admin/testimonial/index'));
    }

    public function ajax_edit($id){
        $this->data['edit_data'] = $this->testimonial_model->get(['id' => $id])->getRowArray();
        echo view('Admin/Testimonial/ajax_edit', $this->data);
    }

    public function edit($id){
        if($this->request->getMethod() === 'post'){
            $data = [
                'name'        => $this->request->getPost('name'),
                'designation' => $this->request->getPost('designation'),
                'date'        => $this->request->getPost('date'),
                'review'      => $this->request->getPost('review'),
                'rating'      => $this->request->getPost('rating'),
                'updated_by' => get_user_id(),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
             $image = $this->upload_file('testimonial','testimonial');
                if($image && valid_file($image['file'])){
    				$data['photo'] = $image['file'];
    			 }
			
			
            $response = $this->testimonial_model->edit($data, ['id' => $id]);
            if ($response){
                session()->setFlashdata('message_success', "testimonial Updated Successfully!");
            }else{
                session()->setFlashdata('message_danger', "Something went wrong! Try Again");
            }
            
        }
        return redirect()->to(base_url('admin/testimonial/index'));
    }

    public function ajax_view($id){
        $this->data['view_data'] = $this->testimonial_model->get(['id' => $id])->getRowArray();
        echo view('Admin/Testimonial/ajax_view', $this->data);
    }

    public function delete($id){
        if($id > 0){
            if ($this->testimonial_model->remove(['id' => $id])){
                session()->setFlashdata('message_success', "testimonial Deleted Successfully!");
            }else{
                session()->setFlashdata('message_danger', "Something went wrong! Try Again");
            }
        }else{
            session()->setFlashdata('message_danger', "Something went wrong! Try Again");
        }
        return redirect()->to(base_url('admin/testimonial/index'));
    }
    
}
