<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminBaseController;
use App\Services\Admin\DashboardService;

class Dashboard extends AdminBaseController
{    
    public function __construct()
    {
        parent::__construct();
    }

    public function index()
    {
        $dashboardService = new DashboardService();
        $this->data['dashboard_data'] = $dashboardService->getDashboardData();

        $this->data['page_title'] = 'Dashboard';
        $this->data['page_name'] = 'Dashboard/index';
        return view('Admin/index', $this->data);
    }
}