<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use <PERSON>Igniter\HTTP\CLIRequest;
use <PERSON>Igniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = [];

    /**
     * Be sure to declare properties for any property fetch you initialized.
     * The creation of dynamic property is deprecated in PHP 8.2.
     */
    // protected $session;

    /**
     * @return void
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Preload any models, libraries, etc, here.

        // E.g.: $this->session = \Config\Services::session();
    }

    public function upload_file($upload_folder, $file_name, $allowed_types = null, $full_url = true, $additional = false)
    {

        $file = $this->request->getFile($file_name);

        if ($file === null) {
            throw new \RuntimeException('No file uploaded with name: ' . $file_name);
        }


        if ($file->isValid() && !$file->hasMoved()) {
            $fileExt = $file->getClientExtension();

            // Check if file type is allowed
            if (!is_null($allowed_types) && !in_array($fileExt, $allowed_types)) {
                log_message('error', 'Disallowed file type: ' . $fileExt);
                return false;
            }


            if ($fileExt == 'pdf') {
                $return['file_type'] = 'pdf';
            } elseif (in_array($fileExt, ['mp3', 'aac'])) {
                $return['file_type'] = 'audio';
            } else {
                $return['file_type'] = 'image';
            }
            
            if($additional){
                $uploadPath = WRITEPATH.'uploads/' . $upload_folder . '/' . date("Ym").'/'.$additional;
            }else{
                $uploadPath = WRITEPATH.'uploads/' . $upload_folder . '/' . date("Ym");
            }

            
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Set file upload options
            $file->move($uploadPath, $file->getRandomName());
            
            if($additional){
                if ($full_url) {
                    $return['file'] = 'uploads/' . $upload_folder . '/' . date("Ym") . '/' . $additional .'/'. $file->getName();
                } else {
                    $return['file'] = date("Ym") . '/' . $additional .'/'. $file->getName();
                }
            }else{
                if ($full_url) {
                    $return['file'] = 'uploads/' . $upload_folder . '/' . date("Ym") . '/' . $file->getName();
                } else {
                    $return['file'] = date("Ym") . '/' . $file->getName();
                }
            }
            return $return;
        } else {
            // Handle error
            log_message('error', $file->getErrorString().'('.$file->getError().')');
            return false;
        }
    }
public function upload_file_public($upload_folder, $file_name, $allowed_types = null, $full_url = true, $additional = false)
    {

        $file = $this->request->getFile($file_name);

        if ($file === null) {
            throw new \RuntimeException('No file uploaded with name: ' . $file_name);
        }


        if ($file->isValid() && !$file->hasMoved()) {
            $fileExt = $file->getClientExtension();

            // Check if file type is allowed
            if (!is_null($allowed_types) && !in_array($fileExt, $allowed_types)) {
                return false;
            }


            if ($fileExt == 'pdf') {
                $return['file_type'] = 'pdf';
            } elseif (in_array($fileExt, ['mp3', 'aac'])) {
                $return['file_type'] = 'audio';
            } else {
                $return['file_type'] = 'image';
            }
            
            if($additional){
                $uploadPath = 'uploads/' . $upload_folder . '/' . date("Ym").'/'.$additional;
            }else{
                $uploadPath = 'uploads/' . $upload_folder . '/' . date("Ym");
            }

            
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Set file upload options
            $file->move($uploadPath, $file->getRandomName());
            
            if($additional){
                if ($full_url) {
                    $return['file'] = 'uploads/' . $upload_folder . '/' . date("Ym") . '/' . $additional .'/'. $file->getName();
                } else {
                    $return['file'] = date("Ym") . '/' . $additional .'/'. $file->getName();
                }
            }else{
                if ($full_url) {
                    $return['file'] = 'uploads/' . $upload_folder . '/' . date("Ym") . '/' . $file->getName();
                } else {
                    $return['file'] = date("Ym") . '/' . $file->getName();
                }
            }
            return $return;
        } else {
            // Handle error
            log_message('error', $file->getErrorString().'('.$file->getError().')');
            return false;
        }
    }
    
    public function upload_multiple_files($upload_folder, $file_name, $allowed_types = null, $full_url = true, $additional = false)
        {
            $files = $this->request->getFileMultiple($file_name);
            $uploaded_files = [];
        
            if (empty($files)) {
                throw new \RuntimeException('No files uploaded with name: ' . $file_name);
            }
        
            if ($additional) {
                $uploadPath = 'uploads/' . $upload_folder . '/' . date("Ym") . '/' . $additional;
            } else {
                $uploadPath = 'uploads/' . $upload_folder . '/' . date("Ym");
            }
        
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }
        
            foreach ($files as $file) {
                if ($file->isValid() && !$file->hasMoved()) {
                    $fileExt = $file->getClientExtension();
        
                    // Check if file type is allowed
                    if (!is_null($allowed_types) && !in_array($fileExt, $allowed_types)) {
                        log_message('error', 'Disallowed file type: ' . $fileExt);
                        continue; // Skip disallowed file
                    }
        
                    // Identify file type
                    if ($fileExt == 'pdf') {
                        $file_type = 'pdf';
                    } elseif (in_array($fileExt, ['mp3', 'aac'])) {
                        $file_type = 'audio';
                    } else {
                        $file_type = 'image';
                    }
        
                    $newName = $file->getRandomName();
                    $file->move($uploadPath, $newName);
        
                    $file_path = $additional
                        ? 'uploads/' . $upload_folder . '/' . date("Ym") . '/' . $additional . '/' . $newName
                        : 'uploads/' . $upload_folder . '/' . date("Ym") . '/' . $newName;
        
                    $uploaded_files[] = [
                        'file' => $full_url ? $file_path : $newName,
                        'file_type' => $file_type,
                        'original_name' => $file->getClientName(),
                    ];
                } else {
                    log_message('error', $file->getErrorString() . '(' . $file->getError() . ')');
                }
            }
        
            return $uploaded_files;
    }
}
