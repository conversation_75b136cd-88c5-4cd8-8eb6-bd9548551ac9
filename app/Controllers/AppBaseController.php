<?php
namespace App\Controllers;
use App\Controllers\BaseController;
abstract class AppBaseController extends BaseController
{
    protected $data = [];

    public function __construct()
    {

    }

    protected function respond(array $data, string $url, int $status = 200)
    {
        if ($this->request->isAJAX()) {
            return $this->response->setStatusCode($status)->setJSON($data);
        }

        if ($data['success']) {
            if($url){
                return redirect()->to($url)->withInput()->with('message_success', $data['message']);
            }
            return redirect()->back()->with('message_success', $data['message']);
        }

        return redirect()->back()->withInput()->with('message_error', $data['message']);
    }

}
