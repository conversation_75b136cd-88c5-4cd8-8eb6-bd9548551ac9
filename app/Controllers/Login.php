<?php

namespace App\Controllers;

use App\Services\Shared\LoginService;

class Login extends WebBaseController
{
    private $loginService;

    public function __construct()
    {
        parent::__construct();
        $this->loginService = new LoginService();
    }

    // public login page
    public function index()
    {
        if ($this->request->getMethod() === 'post') {
            return $this->processLogin();
        }

        if (is_logged_in()) {
            return redirect()->to(base_url('admin/dashboard/index'));
        }

        return view('Frontend/Login/index', $this->data);
    }

    // admin login page
    public function admin_login()
    {
        if ($this->request->getMethod() === 'post') {
            return $this->processLogin();
        }

        if (is_logged_in()) {
            return redirect()->to(base_url('admin/dashboard/index'));
        }

        return view('Frontend/Login/admin_login', $this->data);
    }

    // logout the user
    public function logout()
    {
        $this->loginService->logout();
        return redirect()->to(base_url('login'));
    }

    // process login
    private function processLogin()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        $login = $this->loginService->authenticate($email, $password);

        if ($login['success']) {
            
            $user = $login['user'];
            session()->setFlashdata('message_success', "Welcome back! <b>{$user['name']}</b>");

            switch ($user['role_id']) {
                case 1: return redirect()->to(base_url('admin/dashboard'));
                case 2: return redirect()->to(base_url('owner/dashboard'));
                case 3: return redirect()->to(base_url('agent/dashboard'));
                case 4: return redirect()->to(base_url('tenant/dashboard'));
                default:
                    session()->setFlashdata('message_error', 'Invalid role!');
                    $this->loginService->logout();
                    return redirect()->to(base_url('login'));
            }
        }

        session()->setFlashdata('message_error', $login['message']);
        return redirect()->back()->withInput();
    }
}
