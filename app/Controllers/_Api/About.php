<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Banner_model;
use App\Models\About_us_model;
use App\Models\About_excellence_model;

class About extends Api
{
    private $users_model;
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->about_us_model = new About_us_model();
        $this->about_excellence_model = new About_excellence_model();
    }
    
    public function index()
    {
        $this->is_valid_request(['GET']);
        $about = $this->about_us_model->get()->getRowArray();
        $excellence = $this->about_excellence_model->get()->getResultArray();
        $datas = [
            'about' => $about,
            'excellence' => $excellence
            ];
        // foreach($datas as $key => $data){
        //     $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
        // }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }



}
