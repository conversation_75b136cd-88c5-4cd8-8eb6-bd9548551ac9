<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Top_areas_model;
use App\Models\Areas_model;

class Top_areas extends Api
{
    private $users_model;
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->top_areas_model = new Top_areas_model();
        $this->areas_model = new Areas_model();
    }
    
   
    
     public function index()
    {
        $this->is_valid_request(['GET']);
        
        $datas = $this->areas_model->get(['is_top_area' => 1])->getResultArray() ?? [];
        foreach($datas as $key => $data){
            $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }


}
