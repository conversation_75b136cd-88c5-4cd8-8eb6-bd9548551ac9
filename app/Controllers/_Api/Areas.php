<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Areas_model;
use App\Models\Area_amenities_model;
use App\Models\Area_images_model;
use App\Models\Properties_model;
use App\Models\Developers_model;

class Areas extends Api
{
    private $users_model;
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->areas_model = new Areas_model();
        $this->area_amenities_model = new Area_amenities_model();
        $this->area_images_model = new Area_images_model();
        $this->properties_model = new Properties_model();
        $this->developers_model = new Developers_model();
        
    }
    
    

     public function index($slug = '')
    {
        log_message('error', 'haiiii');

        $this->is_valid_request(['GET']);
        
        $where = [];
        if(!empty($slug)){
            $where['slug'] = $slug;
        }
        
        $area_properties = [];
        $datas = $this->areas_model->get($where)->getResultArray() ?? [];
        $properties = $this->properties_model->get()->getResultArray();
        
        foreach($datas as $key => $data){
            log_message('error', 'inside foreach datas');
            $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
            $datas[$key]['amenities'] = $this->area_amenities_model->get_join(
                [['amenities','amenities.id = area_amenities.amenties_id']],
                ['area_id' => $data['id']],
                ['amenities.title','area_amenities.value']
                )->getResultArray();
                
                $gallery_images = $this->area_images_model->get(['area_id' => $data['id']],['id','files'])->getResultArray();
                $datas[$key]['gallery_images'] = [];
    
                foreach ($gallery_images as $gallery_image) {
                if ($gallery_image && isset($gallery_image['files'])) {
                    $datas[$key]['gallery_images'][] = base_url($gallery_image['files']);
                }
            }
       
            $area_title = $data['title'];
     
            foreach($properties as $prop){ // area properties with area title as property location
            log_message('error', 'inside foreach properties');
                $location = $prop['location'];
                $developer_details = $this->developers_model->get(['id' => $prop['developer_id']],['id','title','description','image'])->getRowArray();
                
                if(!empty($developer_details)){
                    $developer_details['image'] = base_url($developer_details['image']) ?? '';
                }
                
                if (strpos($location, $area_title) !== false) {
                    $prop = [
                        'id'                    => $prop['id'],
                        'developer_id'          => $prop['developer_id'],
                        'developer_details'     => $developer_details,
                        'title'                 => $prop['title'],
                        'thumbnail'             => base_url($prop['thumbnail']),
                        'short_description'     => $prop['short_description'],
                        'category'              => $prop['category'] == 1 ? 'Buy' : 'Rent',
                        'type'                  => $prop['type'] == 1 ? 'Appartment' : 'Villas',
                        'location'              => $prop['location'],
                        'featured'              => $prop['featured'],
                        'bathroom'              => $prop['bathroom'],
                        'room'                  => $prop['room'],
                        'square_feet'           => $prop['square_feet'],
                        'price'                 => $prop['price'],
                        'down_payment'          => $prop['down_payment'],
                        'monthly_payment'       => $prop['monthly_payment'],
                        'down_payment_price'    => $prop['down_payment_price'],
                    ];
                    $area_properties[] = $prop;
                }
            }
            $datas[$key]['properties'] = $area_properties;
        }
        
        log_message('error', 'FINAL');

        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }
    
    public function filter()
    {
        $this->is_valid_request(['POST']);
        
        $where = [];
        
        if ($this->request->getPost('title')) {
            $where['LIKE title'] = $this->request->getPost('title'); // triggers the LIKE logic in your get() method
        }
        
        $datas = $this->areas_model->get($where)->getResultArray() ?? [];
        foreach($datas as $key => $data){
            $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }


}
