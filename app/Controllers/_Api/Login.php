<?php
//File: app/Controllers/Api/Login.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Users_model;
use App\Models\User_role_model;
use App\Models\Course_model;
use App\Models\Subject_model;


class Login extends Api
{
    private $users_model;
    private $course_model;
    private $subject_model;
    
    
    
    public function __construct(){
        $this->users_model = new Users_model();
        $this->course_model = new Course_model();
        $this->subject_model = new Subject_model();
    }
    
    /*** User Login ***/
    public function index()
    {
        $this->is_valid_request(['GET']);
        $phone = $this->request->getGet('phone');
        $code = $this->request->getGet('code');
        
        $device_id = $this->request->getGet('device_id');
        $this->response_data = $this->users_model->login_phone($code, $phone,$device_id);
        return $this->set_response();
    }
    
    /*** Verify otp ***/
    public function verify_otp(){
        $this->is_valid_request(['GET']);
        $user_id = $this->request->getGet('user_id');
        $otp = $this->request->getGet('otp');
        $device_id = $this->request->getGet('device_id');
        $this->response_data = $this->users_model->verify_otp($user_id, $otp, $device_id);
        return $this->set_response();
    }
    
    /*** Register New User ***/
    public function register()
    {
        $this->is_valid_request(['GET']);
        $phone = $this->request->getGet('phone');
        $code = $this->request->getGet('code');
        $name = $this->request->getGet('name');
        $course = $this->request->getGet('course_id');
        $elective_subjects = $this->request->getGet('elective_subjects');
        $referred_by = $this->request->getGet('referred_by');
        $this->response_data = $this->users_model->register_phone($code,$phone,$name,$course, $elective_subjects, $referred_by);
        return $this->set_response();
    }
    
    public function all_courses()
    {
        $this->is_valid_request(['GET']); 
        $courses = $this->course_model->get([],['id','short_code','title'])->getResultArray();
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $courses];
        return $this->set_response();
    }
    
    public function get_elective_subjects()
    {
        $this->is_valid_request(['GET']); 
        $course_id = $this->request->getGet('course_id');
        $data = $this->subject_model->get(['course_id' => $course_id,'subject_type' => 2])->getResultArray() ?? [];
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $data];
        return $this->set_response();
    }

}
