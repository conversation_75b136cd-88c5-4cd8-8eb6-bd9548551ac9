<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Feed_model;
use App\Models\Feed_watched_model;
use App\Models\Feed_like_model;

use App\Models\Stories_model;

class Feed extends Api
{
    private $users_model;
    public function __construct(){
        $this->feed_model = new Feed_model();
        $this->feed_watched_model = new Feed_watched_model();
        $this->feed_like_model = new Feed_like_model();
        
         $this->stories_model = new Stories_model();
    }
    
    /*** Feed List ***/
    public function index()
    {
        $this->is_valid_request(['GET']);
        $reels = $this->feed_model->get(['feed_type' => 2],['id','title','content','feed_category_id','course_id','video_url'])->getResultArray();
        foreach($reels as $key=> $reel){
            if (str_contains($reel['video_url'], 'vimeo')) {
                $reels[$key]['video_type'] = 'vimeo';
            }
            elseif (str_contains($reel['video_url'], 'mp4')) {
                $reels[$key]['video_type'] = 'mp4';
            }
            elseif (str_contains($reel['video_url'], 'youtube')) {
                $reels[$key]['video_type'] = 'youtube';
            }
           

            $reels[$key]['is_liked'] =  $this->feed_like_model->get(['feed_id' => $reel['id'], 'user_id' => $this->user_id])->getNumRows()>0 ? 1 : 0;;
            $reels[$key]['views']    =  $this->feed_watched_model->get(['feed_id' => $reel['id']])->getNumRows();
            $reels[$key]['likes']    =  $this->feed_like_model->get(['feed_id' => $reel['id']])->getNumRows();
            $reels[$key]['thumbnail'] = 'https://project.trogon.info/ec_tution_master/uploads/demo2.jpg';
            $reels[$key]['video_thumbnail'] = 'https://project.trogon.info/ec_tution_master/uploads/demo2.jpg';
        }
        
        $photos = $this->feed_model->get(['feed_type' => 1],['id','title','content','feed_category_id','course_id','image'])->getResultArray();
        foreach($photos as $key=> $photo){
            $photos[$key]['image']    =  valid_file($photo['image']) ? base_url(get_file($photo['image'])) : '';
            $photos[$key]['is_liked'] =  $this->feed_like_model->get(['feed_id' => $photo['id'], 'user_id' => $this->user_id])->getNumRows()>0 ? 1 : 0;;
            $photos[$key]['likes']    =  $this->feed_like_model->get(['feed_id' => $photo['id']])->getNumRows();
        }
        
        
        $stories = $this->stories_model->get(['date' => date('Y-m-d')],['id','title','image'])->getResultArray();
        if(!empty($stories))
        {
            foreach($stories as $key=> $str){
                $stories[$key]['image']    =  valid_file($str['image']) ? base_url(get_file($str['image'])) : '';
            }
        }
        
        $data = [
            'reels'  => $reels,
            'photo' => $photos,
            'stories' =>$stories
        ];
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $data];
        return $this->set_response();
    }
    
    /*** Feed Watched Status Store ***/
    public function feed_watched(){
        $this->is_valid_request(['GET']);
        $feed_id = $this->request->getGet('feed_id');
        $watched = $this->feed_watched_model->get(['feed_id' => $feed_id, 'user_id' => $this->user_id])->getNumRows();
        if($watched==0){
            $data['feed_id']  = $feed_id;
            $data['user_id']    = $this->user_id;
            $data['created_by'] = $this->user_id;
            $data['created_at'] = date('Y-m-d H:i:s');
            $this->feed_watched_model->add($data);
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => []];
        return $this->set_response();
    }
    
    /*** Feed Liked Status Store ***/
    public function feed_like(){
        $this->is_valid_request(['GET']);
        $feed_id = $this->request->getGet('feed_id');
        $liked = $this->feed_like_model->get(['feed_id' => $feed_id, 'user_id' => $this->user_id])->getNumRows();
        if($liked > 0){
            $this->feed_like_model->remove(['feed_id' => $feed_id, 'user_id' => $this->user_id]);
        }else{
            $data['feed_id']  = $feed_id;
            $data['user_id']    = $this->user_id;
            $data['created_by'] = $this->user_id;
            $data['created_at'] = date('Y-m-d H:i:s');

            $this->feed_like_model->add($data);
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => []];
        return $this->set_response();
        
    }



}
