<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Contact_us_model;


class Contact_us extends Api
{
    private $users_model;
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->contact_us_model = new Contact_us_model();
    }
    
   
    
    public function index()
    {   
        // get in touch
        $this->is_valid_request(['POST']);
        
        $data['first_name'] = $this->request->getPost('first_name');
        $data['last_name'] = $this->request->getPost('last_name');
        $data['email'] = $this->request->getPost('email');
        $data['message'] = $this->request->getPost('message');
        $data['created_at'] = date('Y-m-d H:i:s');
     
        $response = $this->contact_us_model->add($data);        
        if($response){
            $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => []];
        }else{
            $this->response_data = ['status' => 0,'message' => 'failed' , 'data' => []];
        }
        return $this->set_response();
    }
    
    public function contact_us()
    {
        // contact us
        $this->is_valid_request(['POST']);
        
        $data['first_name'] = $this->request->getPost('first_name');
        $data['email'] = $this->request->getPost('email');
        $data['phone'] = $this->request->getPost('phone');
        $data['message'] = $this->request->getPost('message');
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $response = $this->contact_us_model->add($data);        
        if($response){
            $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => []];
        }else{
            $this->response_data = ['status' => 0,'message' => 'failed' , 'data' => []];
        }
        return $this->set_response();
    }

}
