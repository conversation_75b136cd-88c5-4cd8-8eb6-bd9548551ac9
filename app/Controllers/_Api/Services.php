<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Services_model;
use App\Models\Services_included_model;


class Services extends Api
{
    private $users_model;
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->services_model = new Services_model();
        $this->services_included_model = new Services_included_model();
    }
    
    public function index()
    {
        $this->is_valid_request(['GET']);
        
        $datas['our_services'] = $this->services_model->get()->getResultArray() ?? [];
        foreach($datas['our_services'] as $key => $data){
            $datas['our_services'][$key]['image'] = $data['image'] != null ? base_url($data['image']) : '';
        }
        
        $datas['whats_included']= $this->services_included_model->get()->getResultArray() ?? [];
        // $datas['whats_included']= [
        //     ['id'=>1,
        //     'title'=> 'At DESCO, we understand that every client\'s real estate journey is unique.'
        //     ],
        //     ['id'=>2,
        //     'title'=> 'Whether you\'re buying your dream home, selling an investment property, or seeking expert advice to grow your portfolio.'
        //     ],
        //     ['id'=>3,
        //     'title'=> 'We offer a full range of services designed to meet your needs at every stage.'
        //     ],['id'=>4,
        //     'title'=> 'Our expert team is committed to providing personalized, professional solutions.'
        //     ],['id'=>5,
        //     'title'=> 'We focus on delivering exceptional results.'
        //     ]
            
            // ];
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }
    


}
