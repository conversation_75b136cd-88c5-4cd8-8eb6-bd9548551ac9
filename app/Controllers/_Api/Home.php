<?php

namespace App\Controllers\Api;
use App\Controllers\Api\Api;
use App\Models\Category_model;
use App\Models\Banner_model;
use App\Models\Users_model;
use App\Models\Properties_model;

class Home extends Api
{
    private $users_model;
    public function __construct(){
        $this->category_model = new Category_model();
        $this->banner_model = new Banner_model();
        $this->users_model = new Users_model();
        $this->properties_model = new Properties_model();
    }
    
    public function search()
    {
        $this->is_valid_request(['GET']);
        
        $where = $this->request->getGet('type');
        
        if($where == 'buy'){
            $datas = $this->properties_model->get(['category' => 1])->getResultArray() ?? [];
            foreach($datas as $key => $data){
                $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
                $datas[$key]['type'] = $data['type'] == 1 ? 'Appartment' : 'Villas';
                $datas[$key]['facilities'] = json_decode($data['facilities'],true);
            }
        }
        
        $data = [];
        $this->response_data = ['status' => 1,'message' => 'Success' , 'data' => $datas];
        return $this->set_response();
    }



}
