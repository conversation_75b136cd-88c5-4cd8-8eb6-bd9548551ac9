<?php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Seo_model;

class Seo extends Api
{
    private $seo_model;
    public function __construct(){
        $this->seo_model = new Seo_model();
    }

    public function seo_key()
    {
        $slug = $this->request->getGet('slug');

        $data = $this->seo_model->get(['slug' => $slug],['id','title','description'])->getResultArray();

        if(empty($data)){
            $this->response_data = ['status' => 0,'message' =>'Failure' , 'data' => []];
        }else{
            $this->response_data = ['status' => 1,'message' =>'Success' , 'data' => $data];
        }
        return $this->set_response();
    }
}