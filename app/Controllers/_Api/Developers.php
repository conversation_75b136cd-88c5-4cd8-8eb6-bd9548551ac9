<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Developers_model;
use App\Models\Properties_model;

class Developers extends Api
{
    private $users_model;
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->developers_model = new Developers_model();
        $this->properties_model = new Properties_model();
    }
    
    public function index($slug='')
    {
        $this->is_valid_request(['GET']);
        
        $where = [];
        if(!empty($slug)){
            $where['slug'] = $slug;
        }
        
        $datas = $this->developers_model->get($where)->getResultArray() ?? [];
        foreach($datas as $key => $data){
            $datas[$key]['image']       = $data['image'] != null ? base_url($data['image']) : '';
            $datas[$key]['properties']  = $this->properties_model->get_properties(null,$data['id']);
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }
    
    public function filter()
    {
        $this->is_valid_request(['POST']);
        
        $where = [];
        
        if ($this->request->getPost('title')) {
            $where['LIKE title'] = $this->request->getPost('title');  
        }
        
        $datas = $this->developers_model->get($where)->getResultArray() ?? [];
        foreach($datas as $key => $data){
            $datas[$key]['image']       = $data['image'] != null ? base_url($data['image']) : '';
            $datas[$key]['properties']  = $this->properties_model->get_properties(null,$data['id']);
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }   
    


}
