<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Properties_model;
use App\Models\Developers_model;
use App\Models\Area_images_model;
use App\Models\Area_amenities_model;
use App\Models\Sale_property_model;

class Properties extends Api
{
    private $users_model;
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->properties_model = new Properties_model();
        $this->developers_model = new Developers_model();
        $this->area_images_model = new Area_images_model();
        $this->area_amenities_model = new Area_amenities_model();
        $this->sale_property_model = new Sale_property_model();
    }
    
    public function buy($slug='')
    {
        $this->is_valid_request(['GET']);
        
        $where = ['category' => 1];
        
        if(!empty($slug)){
            $where['slug'] = $slug;
        }
        
        $datas = $this->properties_model->get($where)->getResultArray() ?? [];
        
        foreach($datas as $key => $data){
            $images = $this->area_images_model->get(['property_id' => $data['id']], ['files'])->getResultArray();
            $datas[$key]['slug'] = $data['slug'];
            $datas[$key]['developer'] = $this->developers_model->get(['id' => $data['developer_id']])->getRow()->title ?? '';
            $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
            $datas[$key]['type'] = $data['type'] == 1 ? 'Apartment' : 'Villa';
            $datas[$key]['category'] = $data['category'] == 1 ? 'Buy' : 'Rent';
            $datas[$key]['facilities'] = json_decode($data['facilities'],true);
            $datas[$key]['amenities'] = $this->area_amenities_model->get_join(
                [['amenities','amenities.id = area_amenities.amenties_id']],
                ['property_id' => $data['id']],
                ['amenities.title','area_amenities.value']
                )->getResultArray();
            $datas[$key]['gallery_images'] = array_map(function ($img) {
                $img['files'] = base_url($img['files']);
                return $img;
            }, $images);
        }
        
        $location = $this->properties_model->get(['category' => 1],['location'])->getResultArray() ?? [];
        $bedroom = $this->properties_model->get(['category' => 1],['room'])->getResultArray() ?? [];
        $price = $this->properties_model->get(['category' => 1],['price'])->getResultArray() ?? [];
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas,'location' => $location,'bedroom' => $bedroom,'price' => $price];
        return $this->set_response();
    }
    
    public function rent($slug='')
    {
        $this->is_valid_request(['GET']);
        
        $where = ['category' => 2];
        
        if(!empty($slug)){
            $where['slug'] = $slug;
        }
        
        $datas = $this->properties_model->get($where)->getResultArray() ?? [];
        foreach($datas as $key => $data){
            $images = $this->area_images_model->get(['property_id' => $data['id']], ['files'])->getResultArray();
            $datas[$key]['slug'] = $data['slug'];

            $datas[$key]['developer'] = $this->developers_model->get(['id' => $data['developer_id']])->getRow()->title ?? '';
            $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
            $datas[$key]['type'] = $data['type'] == 1 ? 'Apartment' : 'Villa';
            $datas[$key]['category'] = $data['category'] == 1 ? 'Buy' : 'Rent';
            $datas[$key]['facilities'] = json_decode($data['facilities'],true);
            $datas[$key]['amenities'] = $this->area_amenities_model->get_join(
                [['amenities','amenities.id = area_amenities.amenties_id']],
                ['property_id' => $data['id']],
                ['amenities.title','area_amenities.value']
                )->getResultArray();
            $datas[$key]['gallery_images'] = array_map(function ($img) {
                $img['files'] = base_url($img['files']);
                return $img;
            }, $images);
        }
        
        $location = $this->properties_model->get(['category' => 2],['location'])->getResultArray() ?? [];
        $bedroom = $this->properties_model->get(['category' => 2],['room'])->getResultArray() ?? [];
        $price = $this->properties_model->get(['category' => 2],['price'])->getResultArray() ?? [];
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas,'location' => $location,'bedroom' => $bedroom,'price' => $price];
        return $this->set_response();
    }
    
    public function buy_filter(){
        $this->is_valid_request(['POST']);
        
        $where = [];
        
        if($this->request->getPost('location')){
            $where['location'] = $this->request->getPost('location');
        }
        
        if($this->request->getPost('room')){
            $where['room'] = $this->request->getPost('room');
        }
        
        if($this->request->getPost('price')){
            $where['price'] = $this->request->getPost('price');
        }
        
        if ($this->request->getPost('title')) {
            $where['LIKE title'] = $this->request->getPost('title'); // triggers the LIKE logic in your get() method
        }
        
        $where['category'] = 1;
        
        $datas = $this->properties_model->get($where)->getResultArray() ?? [];
        foreach($datas as $key => $data){
            $images = $this->area_images_model->get(['property_id' => $data['id']], ['files'])->getResultArray();
            
            $datas[$key]['developer'] = $this->developers_model->get(['id' => $data['developer_id']])->getRow()->title ?? '';
            $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
            $datas[$key]['type'] = $data['type'] == 1 ? 'Apartment' : 'Villa';
            $datas[$key]['category'] = $data['category'] == 1 ? 'Buy' : 'Rent';
            $datas[$key]['facilities'] = json_decode($data['facilities'],true);
            $datas[$key]['amenities'] = $this->area_amenities_model->get_join(
                [['amenities','amenities.id = area_amenities.amenties_id']],
                ['property_id' => $data['id']],
                ['amenities.title','area_amenities.value']
                )->getResultArray();
            $datas[$key]['gallery_images'] = array_map(function ($img) {
                $img['files'] = base_url($img['files']);
                return $img;
            }, $images);
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }
    
    public function rent_filter()
    {
        $this->is_valid_request(['POST']);
        
        $where = [];
        // print_r($this->request->getPost()); exit;
        if($this->request->getPost('location')){
            $where['location'] = $this->request->getPost('location');
        }
        
        if($this->request->getPost('room')){
            $where['room'] = $this->request->getPost('room');
        }
        
        if($this->request->getPost('price')){
            $where['price'] = $this->request->getPost('price');
        }
        
        if ($this->request->getPost('title')) {
            $where['LIKE title'] = $this->request->getPost('title'); // triggers the LIKE logic in your get() method
        }
        
        $where['category'] = 2;
        
        $datas = $this->properties_model->get($where)->getResultArray() ?? [];
        foreach($datas as $key => $data){
            $images = $this->area_images_model->get(['property_id' => $data['id']], ['files'])->getResultArray();
            
            $datas[$key]['developer'] = $this->developers_model->get(['id' => $data['developer_id']])->getRow()->title ?? '';
            $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
            $datas[$key]['type'] = $data['type'] == 1 ? 'Apartment' : 'Villa';
            $datas[$key]['category'] = $data['category'] == 1 ? 'Buy' : 'Rent';
            $datas[$key]['facilities'] = json_decode($data['facilities'],true);
            $datas[$key]['amenities'] = $this->area_amenities_model->get_join(
                [['amenities','amenities.id = area_amenities.amenties_id']],
                ['property_id' => $data['id']],
                ['amenities.title','area_amenities.value']
                )->getResultArray();
            $datas[$key]['gallery_images'] = array_map(function ($img) {
                $img['files'] = base_url($img['files']);
                return $img;
            }, $images);
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }
    
    public function featured($slug = '')
    {
        if(!empty($slug)){
            $where['slug'] = $slug;
        }
        
        $where['featured'] = 1;
        
        $datas = $this->properties_model->get($where)->getResultArray() ?? [];
        foreach($datas as $key => $data){
            $images = $this->area_images_model->get(['property_id' => $data['id']], ['files'])->getResultArray();
            $datas[$key]['slug'] = $data['slug'];
            $datas[$key]['developer'] = $this->developers_model->get(['id' => $data['developer_id']])->getRow()->title ?? '';
            $datas[$key]['thumbnail'] = $data['thumbnail'] != null ? base_url($data['thumbnail']) : '';
            $datas[$key]['type'] = $data['type'] == 1 ? 'Apartment' : 'Villa';
            $datas[$key]['category'] = $data['category'] == 1 ? 'Buy' : 'Rent';
            $datas[$key]['facilities'] = json_decode($data['facilities'],true);
            $datas[$key]['amenities'] = $this->area_amenities_model->get_join(
                [['amenities','amenities.id = area_amenities.amenties_id']],
                ['property_id' => $data['id']],
                ['amenities.title','area_amenities.value']
                )->getResultArray();
            $datas[$key]['gallery_images'] = array_map(function ($img) {
                $img['files'] = base_url($img['files']);
                return $img;
            }, $images);
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }
    
    public function sale_property(){
        
        $this->is_valid_request(['POST']);
        
        $data['title']          = $this->request->getPost('title');
        $data['email']          = $this->request->getPost('email');
        $data['phone']          = $this->request->getPost('phone');
        $data['property_type']  = $this->request->getPost('property_type');
        // $data['address']        = $this->request->getPost('address');
        $data['message']        = $this->request->getPost('message');
        $data['created_at']     = date('Y-m-d H:i:s');
        // $file = $this->upload_file_public('sale_property','image');
        // if(!empty($file)){
        //     $data['image'] = $file['file'];
        // }
        
        $this->sale_property_model->add($data);
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => []];
        return $this->set_response();
    }


}
