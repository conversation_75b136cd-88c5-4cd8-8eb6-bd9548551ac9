<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Models\Reviews_model;

use App\Models\Stories_model;

class Review extends Api
{
    private $reviews_model;
    
    public function __construct()
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->reviews_model = new Reviews_model();
    }

    public function index($title='')
    {
        $where = [];
        
        if(!empty($title)){
            $where['title'] = $title;
        }
        $data = [
            'reviews'  => $this->reviews_model->get([],['id','name','avatar','created_at','rating','title','content',])->getResultArray(),
        ];
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $data];
        return $this->set_response();
    }
    

}
