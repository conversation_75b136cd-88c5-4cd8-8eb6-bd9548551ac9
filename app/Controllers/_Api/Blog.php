<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Blog_model;


class Blog extends Api
{
    private $users_model;
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->blog_model = new Blog_model();
    }
    
   
    
     public function index($slug='')
    {
        $this->is_valid_request(['GET']);
        
        $where = [];
        if(!empty($slug)){
            $where['slug'] = $slug;
        }
        
        $datas = $this->blog_model->get($where)->getResultArray();
        
        foreach($datas as $key => $data){
            
            $created_at = $data['created_at'];
            
            // Convert both dates to timestamps
            $createdTimestamp = strtotime($created_at);
            $nowTimestamp = time();
            
            // Calculate difference in seconds
            $diffInSeconds = $nowTimestamp - $createdTimestamp;
            
            // Convert seconds to days
            $diffInDays = floor($diffInSeconds / (60 * 60 * 24));
            
            $datas[$key]['posted_ago'] = $diffInDays . ' days.';            
            $datas[$key]['image'] = $data['image'] != null ? base_url($data['image']) : '';
        }
        
        $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $datas];
        return $this->set_response();
    }


}
