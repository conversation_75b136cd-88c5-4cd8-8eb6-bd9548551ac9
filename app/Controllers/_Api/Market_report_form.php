<?php
//File: app/Controllers/Api/Home.php

namespace App\Controllers\Api;

use App\Controllers\Api\Api;
use App\Models\Market_report_model;


class Market_report_form extends Api
{
    private $users_model;
        
    public function __construct(){
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, X-Requested-With');

        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
            header('HTTP/1.1 200 OK');
            exit();
        }
        $this->market_report_model = new Market_report_model();
        
        $this->auth_key = '123';
    }
    
    public function get_market_report(){
        
        $this->is_valid_request(['POST']);
        
        $request_authKey = $this->request->getHeaderLine('X-Auth-Key');
        
        $data = $this->market_report_model->get()->getResultArray();
        
        if($this->auth_key == $request_authKey){
        
            if($data){
               $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $data];
            }else{
                $this->response_data = ['status' => 0,'message' => 'failed' , 'data' => []];
            }
        }else{
            $this->response_data = ['status' => 0,'message' => 'User Not Authenticated!!!' , 'data' => []];
        }
        return $this->set_response();
        
    }

    
   
    
    public function index(){
        $this->is_valid_request(['POST']);
        
        $data['first_name'] = $this->request->getPost('first_name');
        $data['last_name'] = $this->request->getPost('last_name');
        $data['email'] = $this->request->getPost('email');
        $data['phone'] = $this->request->getPost('phone');
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $response = $this->market_report_model->add($data);   
        
        $name = $data['first_name'].$data['last_name'];
        $phone_full = $data['phone'];
        
        if($response){
            
            // $this->send_registration_email($data['name'],$data['phone'],$data['whatsapp']);
            // $registrationDate = date('d-m-Y');  
            // $body = "<!DOCTYPE html>
            //     <html lang=\"en\">
            //     <head>
            //         <meta charset=\"UTF-8\">
            //         <title>New User Registration Notification</title>
            //     </head>
            //     <body style=\"font-family: Arial, sans-serif; background-color: #f4f4f4; color: #333; padding: 20px;\">
            //         <div style=\"max-width: 600px; margin: 0 auto; background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\">
            //             <h2 style=\"text-align: center; color: #333;\">New User Registration</h2>
            //             <p>Dear Admin,</p>
            //             <p>We are pleased to inform you that a new user has registered on your platform. Here are the details:</p>
            //             <p><strong>Name:</strong> $name</p>
            //             <p><strong>Phone:</strong> $phone_full</p>
            //             <p><strong>Registration Date:</strong> $registrationDate</p>
            //             <a href='https://trogon.info/tutorpro/medline/admin/student'>Click here to redirect to admin panel!</a>
            //             <p>Please review the registration details at your earliest convenience.</p>
            //             <p>Best regards,<br>Your Platform Team</p>
            //         </div>
            //     </body>
            // </html>";
            // $subject        = "New User Registration: $name";
            // send_email_message('<EMAIL>', 'test', $subject, $body, 'Admin');
            
            // $pdf = base_url(get_settings('market_report_form_url'));
            
            $file = get_settings('market_report_form_url');
         
            $downloadURL = "https://project.trogon.info/desco/api/market_report_form/download_pdf?file=$file";
        
            $this->response_data = ['status' => 1,'message' => 'succesfully' , 'data' => $downloadURL ];
        }else{
            $this->response_data = ['status' => 0,'message' => 'failed' , 'data' => []];
        }
        return $this->set_response();
    }
    
    public function download_pdf()
    {
        $file = $this->request->getGet('file');
  
        $file = basename($file);
    
        $filepath = FCPATH . 'uploads/pdf_market_report/202505/' . $file;

        if (file_exists($filepath)) {
            return $this->response
                ->setHeader('Access-Control-Allow-Origin', '*')
                ->setHeader('Content-Description', 'File Transfer')
                ->setHeader('Content-Type', 'application/octet-stream')
                ->setHeader('Content-Disposition', 'attachment; filename="' . $file . '"')
                ->setHeader('Expires', '0')
                ->setHeader('Cache-Control', 'must-revalidate')
                ->setHeader('Pragma', 'public')
                ->setHeader('Content-Length', filesize($filepath))
                ->setBody(file_get_contents($filepath));
        } else {
            return $this->response->setStatusCode(404)->setBody('File not found.');
        }
    }
    


}
