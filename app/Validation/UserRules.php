<?php
namespace App\Validation;

class UserRules
{
    public function is_unique_soft(string $value, string $params, array $data, ?string &$error = null): bool
    {
        [$table, $field, $excludeId] = array_pad(explode('.', $params), 3, null);

        $db = \Config\Database::connect();
        $builder = $db->table($table);

        $builder->where($field, $value);
        $builder->where('deleted_at', null);

        if (!empty($excludeId) && is_numeric($excludeId)) {
            $builder->where('id !=', $excludeId);
        }

        // Use FALSE to avoid resetting internal state (prevents "reset() on null" error)
        $exists = $builder->countAllResults(false) > 0;

        if ($exists) {
            $error = ucfirst($field) . ' must be unique.';
            return false;
        }

        return true;
    }
}
