
<?php if (!empty($properties)): log_message('error','properties inside view'.print_r($this->data,true));?>

    <table class="table table-bordered">
        <thead>
            <tr>
                <th>#</th>
                <th>Title</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($properties as $index => $property): ?>
                <tr>
                    <td><?= $index + 1 ?></td>
                    <td><?= esc($property['title']) ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
<?php else: ?>
    <p>No properties found.</p>
<?php endif; ?>
