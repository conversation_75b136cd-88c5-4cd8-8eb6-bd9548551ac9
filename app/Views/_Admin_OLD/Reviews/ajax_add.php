 
<form class="form-horizontal" action="<?=base_url('admin/Reviews/add')?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
		
		<div class="form-group col-12 p-0">
        	<label for="name" class="col-sm-12 col-form-label text-muted">
        		Name<span class="text-danger">*</span>
        	</label>
        	<div class="col-sm-12">
        		<input type="text" class="form-control" id="name" name="name" title="name" required>
        	</div>
        
        	<label for="title" class="col-sm-12 col-form-label text-muted">
        		Title<span class="text-danger">*</span>
        	</label>
        	<div class="col-sm-12">
        		<input type="text" class="form-control" id="title" name="title" title="title" required>
        	</div>
        	
        	<label for="photo" class="col-sm-12 col-form-label text-muted">
        		Photo<span class="text-danger">*</span>
        	</label>
        	<div class="col-sm-12">
        		<input type="file" class="form-control" id="photo" name="photo" title="photo" required>
        	</div>
        
        	<label for="content" class="col-sm-12 col-form-label text-muted">
        		Content<span class="text-danger">*</span>
        	</label>
        	<div class="col-sm-12">
        		<input type="text" class="form-control" id="content" name="content" title="content" required>
        	</div>
        
        	<label for="rating" class="col-sm-12 col-form-label text-muted">
        		Star Count (1 to 5)<span class="text-danger">*</span>
        	</label>
        	<div class="col-sm-12">
        		<input type="number" class="form-control" id="rating" name="rating" min="1" max="5" title="star count" required>
        	</div>
        </div>

		
    	<div class="col-12 mt-3" >
    		<button type="submit"  class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
    			<small><i class="fa fa-check"></i></small> Save
    		</button>
    	</div>
	</div>
</form>


<script type="application/javascript">
	$('.select2').select2();

</script>
 