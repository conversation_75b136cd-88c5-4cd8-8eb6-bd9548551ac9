 
<form class="form-horizontal" action="<?= base_url('admin/career/add') ?>" method="post" enctype="multipart/form-data">
	<div class="row" style="margin: 0!important;">

		<div class="form-group col-12 p-0">
			<label for="title" class="col-sm-12 col-form-label text-muted">Title<span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="text" class="form-control" id="title" name="title"  title="Enter title" required>
			</div>
		</div>
		<div class="form-group col-12 p-0">
			<label for="description" class="col-sm-12 col-form-label text-muted">Description<span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<textarea class="form-control" id="description" name="description" title="Enter description" required></textarea>
			</div>
		</div>
		<div class="form-group col-12 p-0">
			<label for="image" class="col-sm-12 col-form-label text-muted">Image<span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="file" class="form-control" id="image" name="image"  title="upload image" required>
			</div>
		</div>
		<div class="form-group col-12 p-0">
			<label for="date" class="col-sm-12 col-form-label text-muted">Date<span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="date" class="form-control" id="date" name="date"  title="Enter date" required>
			</div>
		</div>
		<div class="form-group col-12 p-0">
			<label for="apply_url" class="col-sm-12 col-form-label text-muted">Apply Url<span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="url" class="form-control" id="apply_url" name="apply_url"  title="Enter apply url" required>
			</div>
		</div>

		<div class="col-12">
			<button type="submit"   class="btn btn-primary btn-mini float-right mt-3" style="float: right!important;width: 120px;">
				<small><i class="fa fa-check"></i></small> Save
			</button>
		</div>
	</div>
</form>


<script type="application/javascript">
	$('.select2').select2();
</script>
