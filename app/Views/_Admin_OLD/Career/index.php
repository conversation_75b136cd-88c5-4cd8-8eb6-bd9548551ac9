<!-- start page title -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0"><?= $page_title ?? '' ?></h4>

            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard/index') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active"><?= $page_title ?? '' ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title mb-0"><?= $page_title ?? '' ?></h5>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-md btn-success rounded-pill float-end"
                                onclick="show_ajax_modal('<?= base_url('admin/career/ajax_add') ?>', 'Add Career')">
                            <i class="mdi mdi-plus"></i>
                            Add <?= $page_title ?? '' ?>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <table id="example1" class="data_table_basic table table-bordered  table-striped align-middle" style="width:100%">
                    <thead>
                    <tr>
                        <th style="width: 50px;">#</th>
                        <th>Title</th>
                        <th>Description</th>
                        <th>Date</th>
                        <th>URL</th>
                        <th style="width: 120px;">Image</th>
                        <th style="width: 100px;">Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php if (isset($list_items)): ?>
                        <?php foreach ($list_items as $key => $item): ?>
                            <tr>
                                <td><?= $key + 1 ?></td>
                                <td><?= $item['title'] ?></td>
                                <td><?= $item['description'] ?></td>
                                <td><?= $item['date'] ?></td>
                                <td><?= $item['apply_url'] ?></td>
                                <td>
                                    <?php if(!empty($item['image'])): ?>
                                        <a href="<?= base_url($item['image']) ?>" target="_blank">
                                            <img class="gallery-img img-fluid mx-auto" 
                                                 src="<?= base_url($item['image']) ?>" 
                                                 style="width:70px;height:auto;" 
                                                 alt="" />
                                        </a>
                                    <?php else: ?>
                                        <img class="gallery-img img-fluid mx-auto" 
                                             src="<?= base_url('uploads/dummy.webp') ?>" 
                                             style="width:70px;height:auto;" 
                                             alt="" />
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="dropdown d-inline-block">
                                        <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="ri-more-fill align-middle"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <a href="javascript::void()" class="dropdown-item edit-item-btn" 
                                                   onclick="show_large_modal('<?= base_url('admin/career/ajax_edit/'.$item['id']) ?>', 'Update Career')">
                                                    <i class="ri-pencil-fill align-bottom me-2 text-muted"></i> Edit
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript::void()" class="dropdown-item remove-item-btn" 
                                                   onclick="delete_modal('<?= base_url('admin/career/delete/'.$item['id']) ?>')">
                                                    <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> Delete
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div><!--end row-->