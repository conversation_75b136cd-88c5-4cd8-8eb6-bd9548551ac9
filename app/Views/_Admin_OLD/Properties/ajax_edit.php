<form class="form-horizontal" action="<?= base_url('admin/properties/edit/' . $item_id) ?>" method="post" enctype="multipart/form-data">
    <div class="row">

        <!-- Basic Info -->
        <div class="col-md-12">
            <div class="card mb-3">
                <div class="card-header bg-light"><strong>Basic Information</strong></div>
                <div class="card-body">
                    <div class="form-group">
                        <label>Title<span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="title" value="<?= esc($edit_data['title']) ?>" required>
                    </div>

                    <div class="form-group">
                        <label>Short Description<span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="short_description" value="<?= esc($edit_data['short_description']) ?>" required>
                    </div>

                    <div class="form-group">
                        <label>Description<span class="text-danger">*</span></label>
                        <textarea class="form-control" name="description" required><?= esc($edit_data['description']) ?></textarea>
                    </div>

                    <div class="form-group">
                        <label>Category<span class="text-danger">*</span></label>
                        <select name="category" class="form-control" required>
                            <option value="1" <?= $edit_data['category'] == 1 ? 'selected' : '' ?>>Buy</option>
                            <option value="2" <?= $edit_data['category'] == 2 ? 'selected' : '' ?>>Rent</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Type<span class="text-danger">*</span></label>
                        <select name="type" class="form-control" required>
                            <option value="1" <?= $edit_data['type'] == 1 ? 'selected' : '' ?>>Apartment</option>
                            <option value="2" <?= $edit_data['type'] == 2 ? 'selected' : '' ?>>Villas</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Developer</label>
                        <select name="developer_id" class="form-control">
                            <option value="">Choose Developer</option>
                            <?php foreach ($developers as $developer): ?>
                                <option value="<?= $developer['id'] ?>" <?= $edit_data['developer_id'] == $developer['id'] ? 'selected' : '' ?>>
                                    <?= esc($developer['title']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Price<span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="price" value="<?= esc($edit_data['price']) ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Down Payment</label>
                        <input type="number" class="form-control" name="down_payment" step="0.01" value="<?= esc($edit_data['down_payment']) ?>" placeholder="Enter percentage">
                        <small class="form-text text-muted">In percentage (e.g., 10.5)</small>
                    </div>
                    
                    <div class="form-group">
                        <label>Monthly Payment</label>
                        <input type="number" class="form-control" name="monthly_payment" value="<?= esc($edit_data['monthly_payment']) ?>" step="0.01" placeholder="Enter percentage">
                        <small class="form-text text-muted">In percentage (e.g., 10.5)</small>
                    </div>
        
                </div>
            </div>
        </div>

        <!-- Media -->
        <div class="col-md-12">
            <div class="card mb-3">
                <div class="card-header bg-light"><strong>Media</strong></div>
                <div class="card-body">

                    <div class="form-group mb-2">
                        <label>Thumbnail Preview</label><br>
                        <img style="width:100px;" src="<?= base_url($edit_data['thumbnail'] ?? 'uploads/dummy.webp'); ?>">
                    </div>

                    <div class="form-group">
                        <label>Thumbnail  </label>
                        <input type="file" class="form-control" name="thumbnail">
                    </div>

                    <div class="form-group">
                        <label>Gallery Images  </label>
                        <input type="file" class="form-control" name="gallery_images[]" multiple>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact & Location -->
        <div class="col-md-12">
            <div class="card mb-3">
                <div class="card-header bg-light"><strong>Contact & Location</strong></div>
                <div class="card-body">
                    
                    <div class="form-group">
                        <label class="font-sm color-text-mutted mb-10">Location</label>
                        <input type="text" id="location" name="location" class="form-control" placeholder="Choose location" value="<?= esc($edit_data['location']) ?>" required>
                        <input type="hidden" id="latitude" name="latitude">
                        <input type="hidden" id="longitude" name="longitude">
                        <input type="hidden" id="place_id" name="place_id">
                        <input type="hidden" id="location_url" name="location_url">
                    </div>

                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" class="form-control" name="email" value="<?= esc($edit_data['email']) ?>">
                    </div>

                    <div class="form-group">
                        <label>Phone</label>
                        <input type="text" class="form-control" name="phone" value="<?= esc($edit_data['phone']) ?>">
                    </div>
                </div>
            </div>
        </div>

        <!-- Property Details -->
        <div class="col-md-12">
            <div class="card mb-3">
                <div class="card-header bg-light"><strong>Property Details</strong></div>
                <div class="card-body">
                    <div class="form-group">
                        <label>Bathroom</label>
                        <input type="number" class="form-control" name="bathroom" value="<?= esc($edit_data['bathroom']) ?>">
                    </div>

                    <div class="form-group">
                        <label>Room</label>
                        <input type="number" class="form-control" name="room" value="<?= esc($edit_data['room']) ?>">
                    </div>

                    <div class="form-group">
                        <label>Square Feet</label>
                        <input type="number" class="form-control" name="square_feet" value="<?= esc($edit_data['square_feet']) ?>">
                    </div>

                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" <?= $edit_data['featured'] == 1 ? 'checked' : '' ?>>
                        <label class="form-check-label" for="is_featured">Set as Featured</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit -->
        <div class="col-md-12">
            <div class="text-end">
                <button type="submit" class="btn btn-primary w-25"><i class="fa fa-check"></i> Update</button>
            </div>
        </div>

    </div>
</form>

<script>
    // 1. First, ensure the callback is globally available
window.initAutocomplete = function() {
  // Check if Google Maps API is loaded
  if (!window.google || !google.maps || !google.maps.places) {
    console.error('Google Maps API not loaded properly');
    return;
  }

  const locationInput = document.getElementById("location");
  if (!locationInput) {
    console.error('Location input element not found');
    return;
  }

  const autocomplete = new google.maps.places.Autocomplete(locationInput, {
    componentRestrictions: { country: "ae" }
  });

  autocomplete.addListener("place_changed", function() {
    const place = autocomplete.getPlace();
    if (!place.geometry) {
      console.warn('Place details not found for input:', locationInput.value);
      return;
    }

    document.getElementById("latitude").value = place.geometry.location.lat();
    document.getElementById("longitude").value = place.geometry.location.lng();
    document.getElementById("place_id").value = place.place_id;
    document.getElementById("location_url").value = place.url;
  });
};

// 2. Load the Google Maps API properly
function loadGoogleMapsAPI() {
  // Check if already loaded
  if (window.google && google.maps && google.maps.places) {
    initAutocomplete();
    return;
  }

  const script = document.createElement('script');
  script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyC5cWzNSMOmCTspYQjGJ_g-j64XMHU2WOQ&libraries=places&callback=initAutocomplete';
  script.async = true;
  script.defer = true;
  document.head.appendChild(script);
}

// 3. Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
  loadGoogleMapsAPI();
});

// 4. Fallback in case DOM is already loaded when this script runs
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  loadGoogleMapsAPI();
}
</script>
<style>
    /* Increase the z-index of the Google Places autocomplete dropdown */
    .pac-container {
        z-index: 9999;
    }
</style>

