<!-- start page title -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0"><?=$page_title ?? ''?></h4>

            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?=base_url('admin/dashboard/index')?>">Dashboard</a></li>
                    <li class="breadcrumb-item active"><?=$page_title ?? ''?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title mb-0"><?=$page_title ?? ''?></h5>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-md btn-primary rounded-pill float-end"
                                onclick="show_ajax_modal('<?=base_url('admin/Properties/ajax_add')?>', 'Add Properties')">
                            <i class="mdi mdi-plus"></i>
                            Add <?=$page_title ?? ''?>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <table id="" class="data_table_basic table table-bordered  table-striped align-middle" style="width:100%">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Title</th>
                            <th>Thumbnail</th>
                            <th>Category</th>
                            <th>Type</th>
                            <th>Price</th>
                            <!--<th>Facilities</th>-->
                            <th>Location</th>
                            <th>Contact</th>
                            <th>Bathroom</th>
                            <th>Room</th>
                            <th>Sq. Ft</th>
                            <th>Featured</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($list_items as $key => $item): ?>
                            <tr>
                                <td><?=$key+1?></td>
                                <td><?=$item['title']?></td>
                                <td>
                                    <img src="<?=base_url($item['thumbnail'] ?? 'uploads/dummy.webp')?>" style="width:80px;" />
                                </td>
                                <td><?=$item['category'] == 1 ? 'Buy' : 'Rent'?></td>
                                <td><?=$item['type'] == 1 ? 'Apartment' : 'Villas'?></td>
                                <td><?=$item['price']?></td>
                                <!--<td>-->
                                <!--    <ul class="mb-0 ps-3">-->
                                <!--        </?php -->
                                <!--            $facilities = json_decode($item['facilities'], true); -->
                                <!--            if (!empty($facilities)) {-->
                                <!--                foreach ($facilities as $facility) {-->
                                <!--                    echo '<li>' . esc($facility) . '</li>';-->
                                <!--                }-->
                                <!--            }-->
                                <!--        ?>-->
                                <!--    </ul>-->
                                <!--</td>                                -->
                                <td><?=$item['location']?></td>
                                <td>
                                    <?=$item['email']?><br>
                                    <?=$item['phone']?>
                                </td>
                                <td><?=$item['bathroom']?></td>
                                <td><?=$item['room']?></td>
                                <td><?=$item['square_feet']?></td>
                                <td>
                                    <?=$item['featured'] == 1 ? 'Yes' : 'No'?>
                                </td>
                                <td class="text-center">
                                    <div class="dropdown d-inline-block">
                                        <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="ri-more-fill align-middle"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <a href="javascript:void(0)" class="dropdown-item edit-item-btn" 
                                                   onclick="show_ajax_modal('<?=base_url('admin/properties/ajax_edit/'.$item['id'])?>', 'Update Property')">
                                                    <i class="ri-pencil-fill align-bottom me-2 text-muted"></i> Edit
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0)" class="dropdown-item remove-item-btn" 
                                                   onclick="delete_modal('<?=base_url('admin/properties/delete/'.$item['id'])?>')">
                                                    <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> Delete
                                                </a>
                                            </li>
                                        </ul>
                                        <hr>
                                        <a href="javascript:void(0)" class="btn btn-outline-primary btn-sm" 
                                           onclick="show_ajax_modal('<?=base_url('admin/properties/amenities_view/'.$item['id'])?>', 'Add Amenities')">
                                             Add Amenities
                                        </a><br>
                                        <a class="btn btn-outline-primary btn-sm m-1" href="javascript::void()" onclick="show_small_modal('<?=base_url('admin/properties/view_gallery/'.$item['id'])?>', 'Gallery View')">
                                             View Gallery
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>

                </table>
            </div>
        </div>
    </div>
</div><!--end row-->