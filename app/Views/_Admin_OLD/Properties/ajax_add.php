<form class="form-horizontal" action="<?= base_url('admin/Properties/add') ?>" method="post" enctype="multipart/form-data">
    <div class="row m-0">
        
        <!-- === Basic Info Section === -->
        <div class="col-12">
            <h5 class="text-muted mt-3 mb-2">Basic Information</h5>
        </div>

        <div class="form-group col-md-6">
            <label>Title <span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="title" required>
        </div>

        <div class="form-group col-md-6">
            <label>Category <span class="text-danger">*</span></label>
            <select name="category" class="form-control" required>
                <option value="1">Buy</option>
                <option value="2">Rent</option>
            </select>
        </div>

        <div class="form-group col-md-6">
            <label>Type <span class="text-danger">*</span></label>
            <select name="type" class="form-control" required>
                <option value="1">Apartment</option>
                <option value="2">Villa</option>
            </select>
        </div>

        <div class="form-group col-md-6">
            <label>Developer</label>
            <select name="developer_id" class="form-control">
                <option value="">Choose Developer</option>
                <?php foreach($developers as $developer){ ?>
                    <option value="<?= $developer['id'] ?>"><?= $developer['title'] ?></option>
                <?php } ?>
            </select>
        </div>

        <!-- === Description Section === -->
        <div class="col-12">
            <h5 class="text-muted mt-4 mb-2">Description</h5>
        </div>

        <div class="form-group col-12">
            <label>Short Description <span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="short_description" required>
        </div>

        <div class="form-group col-12">
            <label>Description <span class="text-danger">*</span></label>
            <textarea class="form-control" name="description" rows="3" required></textarea>
        </div>

        <!-- === Media Section === -->
        <div class="col-12">
            <h5 class="text-muted mt-4 mb-2">Media</h5>
        </div>

        <div class="form-group col-md-6">
            <label>Thumbnail <span class="text-danger">*</span></label>
            <input type="file" class="form-control" name="thumbnail" required>
        </div>

        <div class="form-group col-md-6">
            <label>Gallery Images <span class="text-danger">*</span></label>
            <input type="file" class="form-control" name="gallery_images[]" multiple required>
        </div>

        <!-- === Details Section === -->
        <div class="col-12">
            <h5 class="text-muted mt-4 mb-2">Details</h5>
        </div>

        <div class="form-group col-md-4">
            <label>Price</label>
            <input type="number" class="form-control" name="price">
        </div>
        
        <div class="form-group col-md-4">
            <label>Down Payment</label>
            <input type="number" class="form-control" name="down_payment" step="0.01" placeholder="Enter percentage">
            <small class="form-text text-muted">In percentage (e.g., 10.5)</small>
        </div>
        
        <div class="form-group col-md-4">
            <label>Monthly Payment</label>
            <input type="number" class="form-control" name="monthly_payment" step="0.01" placeholder="Enter percentage">
            <small class="form-text text-muted">In percentage (e.g., 10.5)</small>
        </div>

        <div class="form-group col-md-4">
            <label>Bathroom</label>
            <input type="number" class="form-control" name="bathroom" min="0">
        </div>

        <div class="form-group col-md-4">
            <label>Room</label>
            <input type="number" class="form-control" name="room" min="0">
        </div>

        <div class="form-group col-md-6">
            <label>Square Feet</label>
            <input type="number" class="form-control" name="square_feet">
        </div>

        <!--<div class="form-group col-md-6">-->
        <!--    <label>Location</label>-->
        <!--    <textarea name="location" class="form-control" rows="2"></textarea>-->
        <!--</div>-->
        
        <div class="form-group">
            <label class="font-sm color-text-mutted mb-10">Location</label>
            <input type="text" id="location" name="location" class="form-control" placeholder="Choose location" required>
            <input type="hidden" id="latitude" name="latitude">
            <input type="hidden" id="longitude" name="longitude">
            <input type="hidden" id="place_id" name="place_id">
            <input type="hidden" id="location_url" name="location_url">
        </div>

        <!-- === Contact Info Section === -->
        <div class="col-12">
            <h5 class="text-muted mt-4 mb-2">Contact Information</h5>
        </div>

        <div class="form-group col-md-6">
            <label>Email</label>
            <input type="email" class="form-control" name="email">
        </div>

        <div class="form-group col-md-6">
            <label>Phone</label>
            <input type="text" class="form-control" name="phone">
        </div>

        <!-- === Additional Options Section === -->
        <div class="col-12">
            <h5 class="text-muted mt-4 mb-2">Options</h5>
        </div>

        <div class="form-group col-12">
            <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured">
                <label class="form-check-label" for="is_featured">
                    Set as Featured
                </label>
            </div>
        </div>

        <!-- === Submit Section === -->
        <div class="col-12 mt-3">
            <button type="submit" class="btn btn-primary float-right" style="width: 120px;">
                <i class="fa fa-check"></i> Save
            </button>
        </div>
    </div>
</form>

<script>
    // 1. First, ensure the callback is globally available
window.initAutocomplete = function() {
  // Check if Google Maps API is loaded
  if (!window.google || !google.maps || !google.maps.places) {
    console.error('Google Maps API not loaded properly');
    return;
  }

  const locationInput = document.getElementById("location");
  if (!locationInput) {
    console.error('Location input element not found');
    return;
  }

  const autocomplete = new google.maps.places.Autocomplete(locationInput, {
    componentRestrictions: { country: "ae" }
  });

  autocomplete.addListener("place_changed", function() {
    const place = autocomplete.getPlace();
    if (!place.geometry) {
      console.warn('Place details not found for input:', locationInput.value);
      return;
    }

    document.getElementById("latitude").value = place.geometry.location.lat();
    document.getElementById("longitude").value = place.geometry.location.lng();
    document.getElementById("place_id").value = place.place_id;
    document.getElementById("location_url").value = place.url;
  });
};

// 2. Load the Google Maps API properly
function loadGoogleMapsAPI() {
  // Check if already loaded
  if (window.google && google.maps && google.maps.places) {
    initAutocomplete();
    return;
  }

  const script = document.createElement('script');
  script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyC5cWzNSMOmCTspYQjGJ_g-j64XMHU2WOQ&libraries=places&callback=initAutocomplete';
  script.async = true;
  script.defer = true;
  document.head.appendChild(script);
}

// 3. Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
  loadGoogleMapsAPI();
});

// 4. Fallback in case DOM is already loaded when this script runs
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  loadGoogleMapsAPI();
}
</script>
<style>
    /* Increase the z-index of the Google Places autocomplete dropdown */
    .pac-container {
        z-index: 9999;
    }
</style>
