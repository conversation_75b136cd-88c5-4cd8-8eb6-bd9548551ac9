
<style>
  .gallery-container {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  }
  
  .gallery-card {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    height: 180px;
  }
  
  .gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  }
  
  .gallery-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .gallery-card:hover .gallery-img {
    transform: scale(1.05);
  }
  
  .delete-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    z-index: 10;
  }
  
  .gallery-card:hover .delete-btn {
    opacity: 1;
  }
  
  .delete-btn:hover {
    background: #dc3545;
    transform: scale(1.1);
  }
  
  .gallery-title {
    color: #343a40;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
  
  .empty-state {
    text-align: center;
    padding: 30px;
    color: #6c757d;
  }
</style>

<div class="gallery-container">
  <h5 class="gallery-title">Uploaded Gallery</h5>
  
  <?php if(empty($files)): ?>
    <div class="empty-state">
      <i class="fas fa-images fa-3x mb-3"></i>
      <p class="mb-0">No images uploaded yet</p>
    </div>
  <?php else: ?>
    <div class="row">
      <?php foreach ($files as $file): ?>
        <div class="col-12   mb-4">
          <div class="gallery-card">
            <img src="<?= base_url($file['files']) ?>" class="gallery-img" alt="Gallery Image">
            <button class="delete-btn" onclick="deleteImage(<?= $file['id'] ?>, this)">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      <?php endforeach; ?>
    </div>
  <?php endif; ?>
</div>

<script>
  function deleteImage(id, element) {
    if (confirm('Are you sure you want to delete this image?')) {
      fetch('<?= base_url('admin/areas/gallery_delete') ?>', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: 'id=' + encodeURIComponent(id)
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Remove the image card from DOM
          element.closest('.col-12').remove();

          alert('Image deleted successfully');

          // If no images left, show empty state
          if (document.querySelectorAll('.gallery-card').length === 0) {
            document.querySelector('.row').innerHTML = `
              <div class="empty-state">
                <i class="fas fa-images fa-3x mb-3"></i>
                <p class="mb-0">No images uploaded yet</p>
              </div>
            `;
          }
        } else {
          alert('Error deleting image: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Delete Error:', error);
        window.location.reload(); // This will reload the page
      });
    }
  }
</script>
