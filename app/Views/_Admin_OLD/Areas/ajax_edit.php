<form class="form-horizontal" action="<?=base_url('admin/areas/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="card p-3 mb-3">
        <h5 class="card-title">Basic Info</h5>
        <div class="form-group">
            <label>Title<span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="title" value="<?= $edit_data['title']; ?>" required>
        </div>
        <div class="form-group">
            <label>Short Description<span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="short_description" value="<?= $edit_data['short_description']; ?>" required>
        </div>
        <div class="form-group">
            <label>Description<span class="text-danger">*</span></label>
            <textarea class="form-control" id="editor" rows="5" name="description" required><?= isset($edit_data['description']) ? esc($edit_data['description']) : '' ?></textarea>
        </div>
        <div class="form-group">
            <label class="font-sm color-text-mutted mb-10">Location</label>
            <input type="text" id="location" name="location" class="form-control" placeholder="Choose location" value="<?= esc($edit_data['location']) ?>" required>
            <input type="hidden" id="latitude" name="latitude">
            <input type="hidden" id="longitude" name="longitude">
            <input type="hidden" id="place_id" name="place_id">
            <input type="hidden" id="location_url" name="location_url">
        </div>
    </div>

    <div class="card p-3 mb-3">
        <h5 class="card-title">Images</h5>
        <?php if (!empty($edit_data['thumbnail'])): ?>
        <div class="mb-3">
            <label>Current Thumbnail:</label><br>
            <img src="<?= base_url($edit_data['thumbnail']); ?>" alt="Thumbnail" style="width:100px;">
        </div>
        <?php endif; ?>
        <div class="form-group">
            <label>Thumbnail</label>
            <input type="file" class="form-control" name="image">
        </div>
        <div class="form-group">
            <label>Gallery Images</label>
            <input type="file" class="form-control" name="images_for_gallery[]" multiple accept="image/*">
        </div>
    </div>

    <!--<div class="card p-3 mb-3">-->
    <!--    <h5 class="card-title">Area Statistics</h5>-->
    <!--    <div class="row">-->
    <!--        <div class="col-md-6 mb-2">-->
    <!--            <label>Population</label>-->
    <!--            <input type="text" class="form-control" name="population" value="</?= $edit_data['population']; ?>">-->
    <!--        </div>-->
    <!--        <div class="col-md-6 mb-2">-->
    <!--            <label>Established (Year)</label>-->
    <!--            <input type="number" class="form-control" name="established" value="</?= $edit_data['established']; ?>">-->
    <!--        </div>-->
    <!--        <div class="col-md-6 mb-2">-->
    <!--            <label>Area</label>-->
    <!--            <input type="number" class="form-control" name="area" value="</?= $edit_data['area']; ?>">-->
    <!--        </div>-->
    <!--        <div class="col-md-6 mb-2">-->
    <!--            <label>Property Count</label>-->
    <!--            <input type="number" class="form-control" name="property_count" value="</?= $edit_data['property_count']; ?>">-->
    <!--        </div>-->
    <!--    </div>-->
    <!--</div>-->

    <!--<div class="card p-3 mb-3">-->
    <!--    <h5 class="card-title">Property Statistics</h5>-->
    <!--    <div class="form-group mb-2">-->
    <!--        <label>Average Price</label>-->
    <!--        <input type="number" class="form-control" name="avg_price" value="</?= $edit_data['avg_price']; ?>">-->
    <!--    </div>-->
    <!--    <div class="row">-->
    <!--        <div class="col-md-6 mb-2">-->
    <!--            <label>Rental Range From</label>-->
    <!--            <input type="number" class="form-control" name="rental_form" value="</?= $edit_data['rental_form']; ?>">-->
    <!--        </div>-->
    <!--        <div class="col-md-6 mb-2">-->
    <!--            <label>Rental Range To</label>-->
    <!--            <input type="number" class="form-control" name="rental_to" value="</?= $edit_data['rental_to']; ?>">-->
    <!--        </div>-->
    <!--    </div>-->
    <!--</div>-->
    
    <div class="form-group col-12 my-3">
            <div class="form-check mt-2">
                <input class="form-check-input" type="checkbox" name="is_top_area" id="is_top_area" <?= $edit_data['is_top_area'] == 1 ? 'checked' : '' ; ?>>
                <label class="form-check-label" for="is_top_area">
                    Set as Top Area
                </label>
            </div>
        </div>

    <div class="text-right">
        <button type="submit" class="btn btn-primary" style="width: 120px;">
            <i class="fa fa-check"></i> Save
        </button>
    </div>
</form>

<script type="text/javascript">
    $(document).ready(function() {
        // Initialize CKEditor
        ClassicEditor
            .create(document.querySelector('#editor'))
            .catch(error => {
                console.error(error);
            });
    });
</script>

<script>
    // const input = document.getElementById("location");
    // const autocomplete = new google.maps.places.Autocomplete(input, {
    //     componentRestrictions: { country: "in" }
    // });

    // autocomplete.addListener('place_changed', function() {
    //     const place = autocomplete.getPlace();
    //     if (!place.geometry) return;

    //     document.getElementById('latitude').value = place.geometry.location.lat();
    //     document.getElementById('longitude').value = place.geometry.location.lng();
    //     document.getElementById('place_id').value = place.place_id;
    //     document.getElementById('place_url').value = place.url;
    // });
</script>

<script>
    // 1. First, ensure the callback is globally available
    window.initAutocomplete = function() {
      // Check if Google Maps API is loaded
      if (!window.google || !google.maps || !google.maps.places) {
        console.error('Google Maps API not loaded properly');
        return;
      }
    
      const locationInput = document.getElementById("location");
      if (!locationInput) {
        console.error('Location input element not found');
        return;
      }
    
      const autocomplete = new google.maps.places.Autocomplete(locationInput, {
        componentRestrictions: { country: "ae" }
      });
    
      autocomplete.addListener("place_changed", function() {
        const place = autocomplete.getPlace();
        if (!place.geometry) {
          console.warn('Place details not found for input:', locationInput.value);
          return;
        }
    
        document.getElementById("latitude").value = place.geometry.location.lat();
        document.getElementById("longitude").value = place.geometry.location.lng();
        document.getElementById("place_id").value = place.place_id;
        document.getElementById("location_url").value = place.url;
      });
    };
    
    // 2. Load the Google Maps API properly
    function loadGoogleMapsAPI() {
      // Check if already loaded
      if (window.google && google.maps && google.maps.places) {
        initAutocomplete();
        return;
      }
    
      const script = document.createElement('script');
      script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyC5cWzNSMOmCTspYQjGJ_g-j64XMHU2WOQ&libraries=places&callback=initAutocomplete';
      script.async = true;
      script.defer = true;
      document.head.appendChild(script);
    }
    
    // 3. Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
      loadGoogleMapsAPI();
    });
    
    // 4. Fallback in case DOM is already loaded when this script runs
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
      loadGoogleMapsAPI();
    }
</script>
<style>
    /* Increase the z-index of the Google Places autocomplete dropdown */
    .pac-container {
        z-index: 9999;
    }
</style>

