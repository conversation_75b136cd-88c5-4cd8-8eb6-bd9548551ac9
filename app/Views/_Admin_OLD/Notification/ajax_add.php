<style>
    .image,
    .video_url {
        display: none;
    }
</style>
<form action="<?=base_url('admin/notification/add')?>" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-12 form-group p-2">
            <label for="title" class="form-label">Title<span class="required text-danger">*</span></label>
            <input type="text" class="form-control" id="title" name="title" required>
        </div>
        <div class="col-12 form-group p-2">
            <label for="title" class="form-label">Description<span class="required text-danger">*</span></label>
           <textarea class="form-textarea editor" name="description" id="editor"></textarea>
        </div>
        <div class="col-12 form-group p-2">
            <label for="title" class="form-label">Category<span class="required text-danger">*</span></label>
           <select class="form-control select2" name="category_id" id="category_id">
               <option avlue="0">Choose category</option>
               <?php foreach($category as $item){?>
                    <option value="<?=$item['id']?>"><?=$item['name']?></option>
                <?php }  ?>    
           </select>
        </div>
        <div class="col-12 form-group p-2">
            <label for="title" class="form-label">Course<span class="required text-danger">*</span></label>
           <select class="form-control select2" name="course_id" id="course_id">
               <option avlue="">Choose course</option>
           </select>
        </div>
        <div class="col-12 form-group p-2 ">
            <label for="title" class="form-label">External Link</label>
            <input type="text" class="form-control" id="external_link" name="external_link"  >
        </div>
        <div class="col-12 form-group p-2 ">
            <label for="title" class="form-label">Show In App</label>
            <input type="checkbox" id="in_app" name="in_app"  >
        </div>
        <div class="col-12 form-group p-2 ">
            <label for="title" class="form-label">Send Push</label>
            <input type="checkbox" id="push" name="push"  >
        </div>
        <div class="col-12 p-2">
            <button class="btn btn-success float-end btn-save" type="submit">
                <i class="ri-check-fill"></i> Save
            </button>
        </div>
    </div>
</form>
  
<script>
    
var base_url = '<?=base_url('admin/')?>';    
    
$('#category_id').change(function() {
    var category_id = $(this).val(); 
        $.ajax({
            type: 'POST',
            url: base_url+'Question/get_course_by_category', // Adjust the URL as per your routes
            data: {
                category_id: category_id
            },
            dataType: 'json',
            success: function(response) {
                // Clear existing options
                $('#course_id').empty();
                // Add new options
                $.each(response, function(index, course) {
                    $('#course_id').append('<option value="' + course.id + '">' + course.title + '</option>');
                });
            }
        });
    });

    




    $(document).ready(function() {
        // Initialize Select2 on the select element
        // $('.select2').select2();
        
        
    });
    
    $(document).ready(function() {
        // Initialize CKEditor
        ClassicEditor
            .create( document.querySelector( '#editor' ) )
            .catch( error => {
                console.error( error );
            } );
    });

</script>