 
<form class="form-horizontal" action="<?=base_url('admin/seo/add')?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">


 		<div class="form-group col-12 p-0">
            <label for="pageSelect" class="col-sm-12 col-form-label text-muted">Select Page <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select id="pageSelect" class="form-control" name="slug">
                    <option value="" hidden>Choose Page</option>
                    <option value="areadetailpage">Area Detail Page</option>
                    <option value="projectdetailpage">Offplan Project Detail Page</option>
                    <option value="aboutus">About Us</option>
                    <option value="areas">Areas</option>
                    <option value="contactus">Contact Us</option>
                    <option value="buy">Buy</option>
                    <option value="developerdetailpage">Developer Detail Page</option>
                    <option value="developer">Developers</option>
                    <option value="featuredpropertydetailpage">Featured Property Detail Page</option>
                    <option value="listwithus">List With Us</option>
                    <option value="home">Home</option>
                    <option value="Offplan">Offplan Page</option>
                    <option value="projects">Offplan Projects Page</option>
                    <option value="rent">Rent</option>
                    <option value="services">Services</option>
                </select>
            </div>
        </div>
		
		<div class="form-group col-12 p-0">
			<label for="title" class="col-sm-12 col-form-label text-muted">Title<span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="text" class="form-control" id="title" name="title" required>
			</div>
		</div>
		
		<div class="form-group col-12 p-0">
			<label for="" class="col-sm-12 col-form-label text-muted">Description</label>
			<div class="col-sm-12">
				<textarea type="text" class="form-control" id="description" name="description" ></textarea>
			</div>
		</div>
		
    	<div class="col-12 mt-3" >
    		<button type="submit"  class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
    			<small><i class="fa fa-check"></i></small> Save
    		</button>
    	</div>
	</div>
</form>


<script type="application/javascript">
	$('.select2').select2();

</script>
 