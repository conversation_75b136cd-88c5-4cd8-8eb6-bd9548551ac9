
<form class="form-horizontal" action="<?=base_url('admin/seo/edit/'.$item_id)?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">

		<div class="form-group col-12 p-0">
            <label for="pageSelect" class="col-sm-12 col-form-label text-muted">Select Page <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select id="pageSelect" class="form-control" name="slug">
                    <option value="" hidden>Choose Page</option>
					<option value="areadetailpage" <?= ($edit_data['slug'] == 'areadetailpage') ? 'selected' : '' ?>>Area Detail Page</option>
					<option value="projectdetailpage" <?= ($edit_data['slug'] == 'projectdetailpage') ? 'selected' : '' ?>>Offplan Project Detail Page</option>
					<option value="aboutus" <?= ($edit_data['slug'] == 'aboutus') ? 'selected' : '' ?>>About Us</option>
					<option value="areas" <?= ($edit_data['slug'] == 'areas') ? 'selected' : '' ?>>Areas</option>
					<option value="contactus" <?= ($edit_data['slug'] == 'contactus') ? 'selected' : '' ?>>Contact Us</option>
					<option value="buy" <?= ($edit_data['slug'] == 'buy') ? 'selected' : '' ?>>Buy</option>
					<option value="developerdetailpage" <?= ($edit_data['slug'] == 'developerdetailpage') ? 'selected' : '' ?>>Developer Detail Page</option>
					<option value="developer" <?= ($edit_data['slug'] == 'developer') ? 'selected' : '' ?>>Developers</option>
					<option value="featuredpropertydetailpage" <?= ($edit_data['slug'] == 'featuredpropertydetailpage') ? 'selected' : '' ?>>Featured Property Detail Page</option>
					<option value="listwithus" <?= ($edit_data['slug'] == 'listwithus') ? 'selected' : '' ?>>List With Us</option>
					<option value="home" <?= ($edit_data['slug'] == 'home') ? 'selected' : '' ?>>Home</option>
					<option value="Offplan" <?= ($edit_data['slug'] == 'Offplan') ? 'selected' : '' ?>>Offplan Page</option>
					<option value="projects" <?= ($edit_data['slug'] == 'projects') ? 'selected' : '' ?>>Offplan Projects Page</option>
					<option value="rent" <?= ($edit_data['slug'] == 'rent') ? 'selected' : '' ?>>Rent</option>
					<option value="services" <?= ($edit_data['slug'] == 'services') ? 'selected' : '' ?>>Services</option>
                </select>
            </div>
        </div>
        
		<div class="form-group col-12 p-0">
			<label for="image" class="col-sm-12 col-form-label text-muted">Title<span class="text-danger">*</span></label>
			<div class="col-sm-12">
				<input type="text" class="form-control" id="title" name="title" value="<?= $edit_data['title']; ?>" required>
			</div>
		</div>
		
		<div class="form-group col-12 p-0">
			<label for="" class="col-sm-12 col-form-label text-muted">Description</label>
			<div class="col-sm-12">
				<textarea type="text" class="form-control" id="description" name="description" ><?= htmlspecialchars($edit_data['description']) ?></textarea>
			</div>
		</div>
        
    	<div class="col-12 mt-3" >
    		<button type="submit" name="add" value="Save" class="btn btn-primary btn-mini float-right" style="float: right!important;width: 120px;">
    			<small><i class="fa fa-check"></i></small> Save
    		</button>
    	</div>
    </div>	
</form>
