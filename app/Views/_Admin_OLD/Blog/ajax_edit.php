<form class="form-horizontal" action="<?= base_url('admin/Blog/edit/' . $edit_data->id) ?>" method="post" enctype="multipart/form-data">
    <div class="row" style="margin: 0!important;">
        
        <!-- Title -->
        <div class="form-group col-12 p-0">
            <label for="title" class="col-sm-12 col-form-label text-muted">Name <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($edit_data->title) ?>" required>
            </div>
        </div>
        <div class="form-group col-12 p-0">
            <label for="slug" class="col-sm-12 col-form-label text-muted">Slug <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="slug" name="slug" value="<?= htmlspecialchars($edit_data->slug) ?>" required>
            </div>
        </div>
        
        <!-- Type -->
        <div class="form-group col-12 p-0">
            <label for="type" class="col-sm-12 col-form-label text-muted">Type <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <select name="type" id="type" class="form-control " required>
                    <option value="">Choose Type</option>
                     <option value="Luxury Real Estate" <?=$edit_data->type == 'Luxury Real Estate' ? 'selected' : '' ?>>Luxury Real Estate</option> 
                     <option value="Investement" <?=$edit_data->type == 'Investement' ? 'selected' : '' ?>>Investement</option> 
                     <option value="Architecture" <?=$edit_data->type == 'Architecture' ? 'selected' : '' ?>>Architecture</option> 
                </select>
            </div>
        </div>
        
        <!-- Thumbnail -->
        <div class="form-group col-12 p-0">
            <label for="image" class="col-sm-12 col-form-label text-muted">Thumbnail <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="file" class="form-control" id="image" name="image" accept="image/*" title="Upload image">
                <?php if (!empty($edit_data->image)): ?>
                    <img src="<?= base_url(get_file($edit_data->image)) ?>" alt="Current Image" class="mt-2" height="80">
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Description -->
        <div class="form-group col-12 p-0">
            <label for="description" class="col-sm-12 col-form-label text-muted">Description <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <textarea class="form-control" id="description" name="description" required><?= htmlspecialchars($edit_data->description) ?></textarea>
            </div>
        </div>
        
        <!-- Description -->
        <div class="form-group col-12 p-0">
            <label for="description" class="col-sm-12 col-form-label text-muted">Short Description <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input class="form-control" id="short_description" name="short_description" value="<?= htmlspecialchars($edit_data->short_description) ?>" placeholder="Enter short description" required>
            </div>
        </div>
        
        <!-- User Name -->
        <div class="form-group col-12 p-0">
            <label for="user_name" class="col-sm-12 col-form-label text-muted">User Name <span class="text-danger">*</span></label>
            <div class="col-sm-12">
                <input type="text" class="form-control" id="user_name" name="user_name" value="<?= htmlspecialchars($edit_data->user_name) ?>" required>
            </div>
        </div>
        
        <!-- Submit Button -->
        <div class="col-12 mt-3">
            <button type="submit" class="btn btn-primary btn-mini float-right" style="width: 120px;">
                <small><i class="fa fa-check"></i></small> Update
            </button>
        </div>
    </div>
</form>

<!-- Select2 Initialization -->
<script type="application/javascript">
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>
