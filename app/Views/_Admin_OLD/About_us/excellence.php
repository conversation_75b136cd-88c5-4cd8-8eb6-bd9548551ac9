<!-- start page title -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0"><?= $page_title ?? '' ?></h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard/index') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active"><?= $page_title ?? '' ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- content table -->
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title mb-0"><?= $page_title ?? '' ?></h5>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-md btn-primary rounded-pill float-end"
                                onclick="show_ajax_modal('<?= base_url('admin/About_us/ajax_add') ?>', 'Add Excellence')">
                            <i class="mdi mdi-plus"></i>
                            Add Excellence
                        </button>
                    </div>
                </div>
            </div>

            <div class="card-body">
    <table class="data_table_basic table table-bordered table-striped align-middle" style="width:100%">
        <thead>
            <tr>
                <th style="width: 50px;">#</th>
                <th>Title</th>
                <th>Subtitle</th>
                <th style="width: 100px;">Action</th>
            </tr>
        </thead>
        <tbody>
            <?php if (isset($list_items) && !empty($list_items)) : ?>
                <?php foreach ($list_items as $key => $item) : ?>
                    <tr>
                        <td><?= $key + 1 ?></td>
                        <td><?= esc($item['title'] ?? '-') ?></td>
                        <td><?= esc($item['subtitle'] ?? '-') ?></td>
                        <td>
                            <div class="dropdown d-inline-block">
                                <button class="btn btn-soft-secondary btn-sm dropdown" type="button"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="ri-more-fill align-middle"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a href="javascript:void(0)" class="dropdown-item edit-item-btn"
                                           onclick="show_ajax_modal('<?= base_url('admin/about_us/ajax_edit/' . $item['id']) ?>', 'Update Entry')">
                                            <i class="ri-pencil-fill align-bottom me-2 text-muted"></i> Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:void(0)" class="dropdown-item remove-item-btn"
                                           onclick="delete_modal('<?= base_url('admin/about_us/delete/' . $item['id']) ?>')">
                                            <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> Delete
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                <?php endforeach ?>
            <?php else : ?>
                <tr>
                    <td colspan="4" class="text-center">No records found</td>
                </tr>
            <?php endif ?>
        </tbody>
    </table>
</div>
        </div>
    </div>
</div>

<script>
    $('#add-title-subtitle').on('click', function() {
    const newTitleSubtitle = `
        <div class="row g-2 title-subtitle-row mb-2">
            <div class="col-md-6">
                <input type="text" class="form-control" name="titles[]" placeholder="Enter Title">
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" name="subtitles[]" placeholder="Enter Subtitle">
            </div>
        </div>`;
    $('#title-subtitle-wrapper').append(newTitleSubtitle);
});

</script>