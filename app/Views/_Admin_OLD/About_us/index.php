<!-- start page title -->
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0"><?= $page_title ?? '' ?></h4>
            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard/index') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active"><?= $page_title ?? '' ?></li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- content table -->
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <form action="<?= base_url('admin/about_us/desco_edit') ?>" method="post">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <!-- Who is DESCO -->
                        <div class="col-lg-12 mb-3">
                            <label for="who_is_desco" class="form-label h5">Who is DESCO <span class="required text-danger">*</span></label>
                            <textarea class="form-control" id="editor" name="who_is_desco" rows="5"><?= isset($content['who_is_desco']) ? esc($content['who_is_desco']) : '' ?></textarea>
                        </div>
                        
                       
                        
                        <div class="col-12">
                            <button class="btn btn-success float-end btn-save" type="submit">
                                <i class="ri-check-fill"></i> Save
                            </button>
                        </div>
                    </div>
                </form>

                <script type="text/javascript">
                    $(document).ready(function() {
                        // Initialize CKEditor
                        ClassicEditor
                            .create(document.querySelector('#editor'))
                            .catch(error => {
                                console.error(error);
                            });
                    });
                </script>
            </div>
        </div>
    </div>
</div>