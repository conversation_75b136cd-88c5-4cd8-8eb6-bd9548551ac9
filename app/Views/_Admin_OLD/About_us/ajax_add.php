
<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <form action="<?= base_url('admin/about_us/add') ?>" method="post"> <?= csrf_field() ?>

                    <div class="row">
                        <!-- Title & Subtitle Dynamic Section -->
                        <div class="col-12 border p-4 border-primary rounded">
                            <label class="form-label">Titles & Subtitles</label>
                            <div id="title-subtitle-wrapper">
                                <div class="row g-2 title-subtitle-row mb-2">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="titles[]" placeholder="Enter Title">
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" name="subtitles[]" placeholder="Enter Subtitle">
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary mt-2" id="add-title-subtitle">
                                <i class="ri-add-fill"></i> Add Title & Subtitle
                            </button>
                        </div>
                
                
                        <div class="col-12">
                            <button class="btn btn-success float-end btn-save" type="submit">
                                <i class="ri-check-fill"></i> Save
                            </button>
                        </div>
                    </div>
                </form>


            </div>
        </div>
    </div>
</div>


<script>
    $('#add-title-subtitle').on('click', function() {
    const newTitleSubtitle = `
        <div class="row g-2 title-subtitle-row mb-2">
            <div class="col-md-6">
                <input type="text" class="form-control" name="titles[]" placeholder="Enter Title">
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" name="subtitles[]" placeholder="Enter Subtitle">
            </div>
        </div>`;
    $('#title-subtitle-wrapper').append(newTitleSubtitle);
});

</script>