<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - THREDEX</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #495057;
            --accent-color: #6c757d;
            --text-dark: #212529;
            --text-muted: #6c757d;
            --border-color: #dee2e6;
            --bg-light: #f8f9fa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
            background-color: var(--bg-light);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .admin-login-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            width: 100%;
            max-width: 400px;
            padding: 40px 32px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .admin-header {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .admin-icon {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 16px;
        }

        .admin-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .admin-subtitle {
            color: var(--text-muted);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 6px;
            display: block;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 14px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 15px;
            transition: border-color 0.15s ease;
            background-color: #fafafa;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: white;
            box-shadow: 0 0 0 2px rgba(73, 80, 87, 0.1);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-muted);
            cursor: pointer;
            padding: 2px;
            font-size: 14px;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .btn-admin {
            width: 100%;
            background-color: var(--primary-color);
            border: none;
            border-radius: 6px;
            padding: 12px;
            font-size: 15px;
            font-weight: 500;
            color: white;
            margin-top: 8px;
            transition: background-color 0.15s ease;
        }

        .btn-admin:hover {
            background-color: #3a4145;
        }

        .btn-admin:active {
            background-color: #2d3338;
        }

        .btn-admin.loading {
            pointer-events: none;
            position: relative;
            color: transparent;
        }

        .btn-admin.loading::after {
            content: '';
            position: absolute;
            width: 14px;
            height: 14px;
            top: 50%;
            left: 50%;
            margin: -7px 0 0 -7px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 0.6s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .forgot-link {
            text-align: center;
            margin-top: 20px;
        }

        .forgot-link a {
            color: var(--accent-color);
            text-decoration: none;
            font-size: 13px;
        }

        .forgot-link a:hover {
            text-decoration: underline;
        }

        .alert {
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
            border: none;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }

        .alert-success {
            background-color: #d1edff;
            color: #0c5460;
        }

        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 24px;
            font-size: 13px;
            color: #856404;
        }

        .security-notice i {
            margin-right: 6px;
        }

        /* Custom Toast Styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .custom-toast {
            min-width: 300px;
            background: white;
            border: none;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .custom-toast.error {
            border-left: 4px solid #dc3545;
        }

        .custom-toast.success {
            border-left: 4px solid #28a745;
        }

        .custom-toast.warning {
            border-left: 4px solid #ffc107;
        }

        .toast-header.error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .toast-header.success {
            background-color: #d4edda;
            color: #155724;
        }

        .toast-header.warning {
            background-color: #fff3cd;
            color: #856404;
        }

        .toast-body {
            padding: 12px 16px;
            font-size: 14px;
        }

        @media (max-width: 480px) {
            .admin-login-card {
                padding: 32px 20px;
                margin: 10px;
            }

            .toast-container {
                top: 10px;
                right: 10px;
                left: 10px;
            }

            .custom-toast {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <!-- Toast Container -->
    <div class="toast-container">
        <div id="errorToast" class="toast custom-toast error" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header error">
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="errorToastBody">
                <!-- Error message will be inserted here -->
            </div>
        </div>

        <div id="successToast" class="toast custom-toast success" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header success">
                <i class="fas fa-check-circle me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="successToastBody">
                <!-- Success message will be inserted here -->
            </div>
        </div>

        <div id="warningToast" class="toast custom-toast warning" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong class="me-auto">Warning</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="warningToastBody">
                <!-- Warning message will be inserted here -->
            </div>
        </div>
    </div>

    <div class="admin-login-card">
        <div class="admin-header">
            <div class="admin-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h1 class="admin-title">THREDEX</h1>
            <p class="admin-subtitle">Admin Portal</p>
        </div>

        <div class="security-notice">
            <i class="fas fa-lock"></i>
            Authorized personnel only. All access is logged and monitored.
        </div>

        <form id="adminLoginForm" method="post" action="<?= base_url('login/admin_login') ?>">
            <div class="form-group">
                <label class="form-label" for="adminEmail">Admin Email</label>
                <input type="email" class="form-control" id="adminEmail" name="email" placeholder="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="adminPassword">Password</label>
                <div class="password-container">
                    <input type="password" class="form-control" placeholder="Enter admin password" id="adminPassword" name="password" required>
                    <button type="button" class="password-toggle" onclick="toggleAdminPassword()">
                        <i class="fas fa-eye" id="adminPasswordIcon"></i>
                    </button>
                </div>
            </div>

            <button type="submit" class="btn btn-admin">
                Access Admin Panel
            </button>
        </form>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleAdminPassword() {
            const passwordInput = document.getElementById('adminPassword');
            const passwordIcon = document.getElementById('adminPasswordIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        // Toast notification functions
        function showToast(type, message) {
            const toastId = type + 'Toast';
            const toastBodyId = type + 'ToastBody';
            const toastElement = document.getElementById(toastId);
            const toastBody = document.getElementById(toastBodyId);
            
            if (toastElement && toastBody) {
                toastBody.textContent = message;
                const toast = new bootstrap.Toast(toastElement, {
                    autohide: true,
                    delay: 5000
                });
                toast.show();
            }
        }

        function showErrorToast(message) {
            showToast('error', message);
        }

        function showSuccessToast(message) {
            showToast('success', message);
        }

        function showWarningToast(message) {
            showToast('warning', message);
        }

        // Check for PHP session flash messages on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check for error messages
            <?php if (session()->getFlashdata('message_error')): ?>
                showErrorToast('<?= addslashes(session()->getFlashdata('message_error')) ?>');
            <?php endif; ?>

            // Check for success messages
            <?php if (session()->getFlashdata('message_success')): ?>
                showSuccessToast('<?= addslashes(session()->getFlashdata('message_success')) ?>');
            <?php endif; ?>

            // Check for warning messages
            <?php if (session()->getFlashdata('message_warning')): ?>
                showWarningToast('<?= addslashes(session()->getFlashdata('message_warning')) ?>');
            <?php endif; ?>
        });

        // Add loading state to form submission
        document.getElementById('adminLoginForm').addEventListener('submit', function() {
            const submitBtn = document.querySelector('.btn-admin');
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>