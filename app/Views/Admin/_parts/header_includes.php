<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">

<style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #34495e;
      --success-color: #27ae60;
      --info-color: #3498db;
      --warning-color: #f39c12;
      --danger-color: #e74c3c;
      --light-color: #ecf0f1;
      --dark-color: #2c3e50;
      --body-bg: #ecf0f1;
      --card-bg: #ffffff;
      --header-height: 60px;
      --bottom-nav-height: 65px;
      --content-padding: 15px;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--body-bg);
      padding-bottom: var(--bottom-nav-height);
      padding-top: var(--header-height);
      color: #525f7f;
    }

    .card {
      border: none;
      border-radius: 15px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
      margin-bottom: 20px;
      overflow: hidden;
    }

    .card-header {
      background-color: var(--card-bg);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      padding: 15px 20px;
    }

    .card-title {
      font-weight: 600;
      color: var(--dark-color);
      margin-bottom: 0;
      font-size: 1.1rem;
    }

    .card-body {
      padding: 20px;
    }

    /* Form styling */
    .form-label {
      font-weight: 500;
      font-size: 0.9rem;
      color: var(--dark-color);
      margin-bottom: 0.5rem;
    }

    .form-control {
      border-radius: 10px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      padding: 10px 15px;
      font-size: 0.95rem;
      transition: all 0.2s;
    }

    .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 0.2rem rgba(86, 100, 210, 0.15);
    }

    .form-text {
      font-size: 0.8rem;
      color: #8898aa;
    }

    /* Mobile App Header */
    .main-header.navbar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: var(--header-height);
      background-color: var(--primary-color);
      padding: 0 15px;
      display: flex;
      align-items: center;
      z-index: 1030;
    }

    .navbar-brand {
      display: flex;
      align-items: center;
      color: white !important;
      font-weight: 600;
      font-size: 1.25rem;
      margin-right: auto;
    }

    .navbar-brand img {
      margin-right: 8px;
    }

    .navbar .nav-link {
      color: rgba(255, 255, 255, 0.8) !important;
      padding: 0.5rem;
      font-size: 1.25rem;
    }

    .navbar .nav-link:hover {
      color: white !important;
    }
    .navbar-brand img {
        margin-right
    : 8px;
        width: 20%;
    }
    /* Mobile Bottom Navigation */
    .mobile-bottom-nav {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      height: var(--bottom-nav-height);
      background-color: white;
      display: flex;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
      z-index: 1020;
    }

    .mobile-bottom-nav .nav-link {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #8898aa;
      position: relative;
      padding: 8px 0;
      transition: color 0.3s ease;
    }

    .mobile-bottom-nav .nav-link.active {
      color: var(--primary-color);
      font-weight: 600;
    }

    .mobile-bottom-nav .nav-link.active:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 24px;
      height: 3px;
      background-color: var(--primary-color);
      border-radius: 3px 3px 0 0;
    }

    .mobile-bottom-nav .nav-icon {
      font-size: 1.25rem;
      margin-bottom: 4px;
    }

    .mobile-bottom-nav .nav-text {
      font-size: 0.7rem;
      font-weight: 500;
    }

    /* Content Wrapper */
    .content-wrapper {
      margin-left: 0 !important;
      padding: 10px;
      min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
      background-color: var(--body-bg);
    }
  
    /* Desktop Styles */
    @media (min-width: 992px) {
      .main-sidebar {
        display: block;
        width: 250px;
        background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 1040;
      }

      .sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link.active {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 10px;
      }

      .nav-sidebar .nav-item .nav-link {
        padding: 12px 15px;
        color: rgba(255, 255, 255, 0.8);
        border-radius: 10px;
        margin-bottom: 5px;
      }

      .nav-sidebar .nav-item .nav-link:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.1);
      }

      .brand-link {
        color: white !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        padding: 15px;
        height: var(--header-height);
        display: flex;
        align-items: center;
      }

      /* Adjust content wrapper */
      .content-wrapper {
        margin-left: 250px !important;
        padding: 20px 30px;
      }

      /* Show footer on desktop */
      .main-footer {
        display: block;
        margin-left: 250px;
        padding: 15px 30px;
        font-size: 0.875rem;
        color: #8898aa;
      }

      /* Hide mobile bottom nav */
      .mobile-bottom-nav {
        display: none;
      }

      body {
        padding-bottom: 0;
      }
    }

    /* Floating Action Button */
    .fab {
      position: fixed;
      bottom: 80px;
      right: 20px;
      width: 56px;
      height: 56px;
      border-radius: 50%;
      background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      transition: all 0.3s ease;
    }

    .fab:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
      color: white;
    }

    @media (min-width: 992px) {
      .fab {
        bottom: 30px;
      }
      .navbar-brand img {
          margin-right: 8px;
          width: 20%!important;
      }
    }
    

       /* Form styling */
       .form-label {
      font-weight: 500;
      font-size: 0.9rem;
      color: var(--dark-color);
      margin-bottom: 0.5rem;
    }

    .form-control {
      border-radius: 10px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      padding: 10px 15px;
      font-size: 0.95rem;
      transition: all 0.2s;
    }

    .form-control:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 0.2rem rgba(86, 100, 210, 0.15);
    }

    .form-text {
      font-size: 0.8rem;
      color: #8898aa;
    }

    /* Step progress */
    .step-progress {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      position: relative;
    }

    .step-progress::before {
      content: '';
      position: absolute;
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #e9ecef;
      z-index: 1;
    }

    .step {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
    }

    .step-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #e9ecef;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
      font-weight: 600;
      color: #8898aa;
      margin-bottom: 8px;
      transition: all 0.3s;
    }

    .step-text {
      font-size: 0.8rem;
      color: #8898aa;
      font-weight: 500;
      text-align: center;
    }

    .step.active .step-icon {
      background-color: var(--primary-color);
      color: white;
    }

    .step.active .step-text {
      color: var(--primary-color);
      font-weight: 600;
    }

    .step.completed .step-icon {
      background-color: var(--success-color);
      color: white;
    }

    /* Form step sections */
    .form-step {
      display: none;
    }

    .form-step.active {
      display: block;
    }

    /* Form navigation */
    .form-navigation {
      display: flex;
      justify-content: space-between;
      margin-top: 30px;
    }

    /* File upload styling */
    .file-upload-wrapper {
      position: relative;
      width: 100%;
      height: 120px;
      border: 2px dashed rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      background-color: rgba(0, 0, 0, 0.02);
      transition: all 0.3s;
      cursor: pointer;
    }

    .file-upload-wrapper:hover {
      border-color: var(--primary-color);
      background-color: rgba(86, 100, 210, 0.05);
    }

    .file-upload-wrapper input[type="file"] {
      position: absolute;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }

    .file-upload-content {
      text-align: center;
    }

    .file-upload-icon {
      font-size: 2rem;
      color: #8898aa;
      margin-bottom: 10px;
    }

    .file-upload-text {
      font-size: 0.9rem;
      color: #8898aa;
    }
  </style>
  <!-- Custom styles for this page -->

  
  <?php if (isset($extraHeadContent)) echo $extraHeadContent; ?>