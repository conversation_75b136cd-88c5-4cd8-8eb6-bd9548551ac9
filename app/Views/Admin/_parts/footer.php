</div><!--/. container-fluid -->
    </section>
    <!-- /.content -->

    <?php if (isset($showFab) && $showFab): ?>
    <!-- Floating Action Button -->
    <a href="<?php echo $fabLink ?? 'agent-onboarding.php'; ?>" class="fab">
      <i class="fas fa-plus"></i>
    </a>
    <?php endif; ?>
  </div>
  <!-- /.content-wrapper -->

  <!-- Main Footer -->
  <footer class="main-footer">
    <strong>Copyright &copy; 2025 <a href="#">THREDEX</a>.</strong>
    All rights reserved.
    <div class="float-right d-none d-sm-inline-block">
      <b>Version</b> 1.0.0
    </div>
  </footer>
</div>
<!-- ./wrapper -->

<!-- REQUIRED SCRIPTS -->
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Bootstrap 5 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>

<!-- Page specific script -->
<script>
  $(function() {
    // Set current date
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    const today = new Date();
    $('#currentDate').text(today.toLocaleDateString('en-US', options));

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Add ripple effect to bottom navigation
    $('.mobile-bottom-nav .nav-link').on('click', function(e) {
      // Create ripple element
      const ripple = $('<span class="nav-ripple"></span>');
      const x = e.pageX - $(this).offset().left;
      const y = e.pageY - $(this).offset().top;

      ripple.css({
        top: y + 'px',
        left: x + 'px'
      }).appendTo($(this));

      // Remove ripple after animation completes
      setTimeout(function() {
        ripple.remove();
      }, 600);
    });

    // Fix hamburger menu toggle
    $('[data-widget="pushmenu"]').on('click', function(e) {
      e.preventDefault();
      // Force remove and add classes instead of toggle to ensure consistent behavior
      if ($('body').hasClass('sidebar-open')) {
        $('body').removeClass('sidebar-open');
        $('.main-sidebar').removeClass('d-block');
      } else {
        $('body').addClass('sidebar-open');
        $('.main-sidebar').addClass('d-block');
      }
    });

    // Close sidebar on mobile
    $('.sidebar-close').on('click', function() {
      $('body').removeClass('sidebar-open');
      $('.main-sidebar').removeClass('d-block');
    });

    // Close sidebar when clicking outside
    $(document).on('click', function(e) {
      if ($('body').hasClass('sidebar-open') &&
          !$(e.target).closest('.main-sidebar').length &&
          !$(e.target).closest('[data-widget="pushmenu"]').length) {
        $('body').removeClass('sidebar-open');
        $('.main-sidebar').removeClass('d-block');
      }
    });
    
    <?php if (isset($customScripts)) echo $customScripts; ?>
  });
</script>

<style>
  /* Ripple effect for mobile navigation */
  .mobile-bottom-nav .nav-link {
    position: relative;
    overflow: hidden;
  }

  .nav-ripple {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
  }

  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }
  
  <?php if (isset($extraStyles)) echo $extraStyles; ?>
</style>
<?php if (isset($extraFooterContent)) echo $extraFooterContent; ?>
</body>
</html>
