<?php

// TODO: Incomplete Page, Need to Redesign, Profile Icon Not working
$showFab = true;
$fabLink = base_url('admin/owners/add');
?>
<style>
  /* Modern Dashboard Cards */
  .dashboard-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    padding: 16px;
    height: 100%;
    min-height: 120px;
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  .dashboard-card-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 42px;
    height: 42px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
  }

  .dashboard-card[data-color="primary"] .dashboard-card-icon {
    color: var(--primary-color);
    background-color: rgba(86, 100, 210, 0.1);
  }

  .dashboard-card[data-color="success"] .dashboard-card-icon {
    color: var(--success-color);
    background-color: rgba(45, 206, 137, 0.1);
  }

  .dashboard-card[data-color="warning"] .dashboard-card-icon {
    color: var(--warning-color);
    background-color: rgba(251, 99, 64, 0.1);
  }

  .dashboard-card[data-color="info"] .dashboard-card-icon {
    color: var(--info-color);
    background-color: rgba(17, 205, 239, 0.1);
  }

  .dashboard-card-content {
    padding-top: 20px;
  }

  .dashboard-card-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 4px;
    line-height: 1;
    color: #32325d;
  }

  .dashboard-card-label {
    font-size: 0.8rem;
    color: #8898aa;
    margin-bottom: 0;
    font-weight: 500;
  }

  /* Quick Action Cards */
  .quick-action-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  @media (min-width: 768px) {
    .quick-action-container {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  .quick-action-item {
    background-color: white;
    border-radius: 16px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .quick-action-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  .quick-action-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 10px;
  }

  .quick-action-item[data-color="primary"] .quick-action-icon {
    color: var(--primary-color);
    background-color: rgba(86, 100, 210, 0.1);
  }

  .quick-action-item[data-color="success"] .quick-action-icon {
    color: var(--success-color);
    background-color: rgba(45, 206, 137, 0.1);
  }

  .quick-action-item[data-color="warning"] .quick-action-icon {
    color: var(--warning-color);
    background-color: rgba(251, 99, 64, 0.1);
  }

  .quick-action-item[data-color="info"] .quick-action-icon {
    color: var(--info-color);
    background-color: rgba(17, 205, 239, 0.1);
  }

  .quick-action-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #32325d;
    margin-bottom: 0;
  }

  /* Alert Cards */
  .alert-card {
    background-color: white;
    border-radius: 16px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .alert-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  .alert-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .alert-card-icon {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
  }

  .alert-card[data-priority="high"] .alert-card-icon {
    color: var(--danger-color);
    background-color: rgba(245, 54, 92, 0.1);
  }

  .alert-card[data-priority="medium"] .alert-card-icon {
    color: var(--warning-color);
    background-color: rgba(251, 99, 64, 0.1);
  }

  .alert-card[data-priority="low"] .alert-card-icon {
    color: var(--info-color);
    background-color: rgba(17, 205, 239, 0.1);
  }

  .alert-card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #32325d;
    margin-bottom: 5px;
  }

  .alert-card-subtitle {
    font-size: 0.75rem;
    color: #8898aa;
    margin-bottom: 0;
  }

  .alert-card-time {
    font-size: 0.7rem;
    color: #8898aa;
  }

  /* Maintenance Requests Card */
  .maintenance-card {
    background-color: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
  }

  .maintenance-card-header {
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .maintenance-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #32325d;
    margin-bottom: 0;
  }

  .maintenance-card-link {
    font-size: 0.8rem;
    color: var(--primary-color);
    text-decoration: none;
  }

  /* Recent Activity Card */
  .activity-card {
    background-color: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
  }

  .activity-card-header {
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .activity-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #32325d;
    margin-bottom: 0;
  }

  .activity-card-link {
    font-size: 0.8rem;
    color: var(--primary-color);
    text-decoration: none;
  }

  .activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .activity-item {
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: flex-start;
  }

  .activity-item:last-child {
    border-bottom: none;
  }

  .activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1rem;
  }

  .activity-icon.bg-primary {
    background-color: rgba(86, 100, 210, 0.1);
    color: var(--primary-color);
  }

  .activity-icon.bg-success {
    background-color: rgba(45, 206, 137, 0.1);
    color: var(--success-color);
  }

  .activity-icon.bg-warning {
    background-color: rgba(251, 99, 64, 0.1);
    color: var(--warning-color);
  }

  .activity-icon.bg-info {
    background-color: rgba(17, 205, 239, 0.1);
    color: var(--info-color);
  }

  .activity-content {
    flex: 1;
  }

  .activity-title {
    font-size: 0.85rem;
    font-weight: 500;
    color: #32325d;
    margin-bottom: 5px;
  }

  .activity-subtitle {
    font-size: 0.75rem;
    color: #8898aa;
    margin-bottom: 0;
  }

  .activity-time {
    font-size: 0.7rem;
    color: #8898aa;
    white-space: nowrap;
    margin-left: 15px;
  }

  /* Animations */
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
</style>

<div class="d-none d-md-block text-end mb-3">
  <span class="text-muted" id="currentDate"></span>
</div>

<?php 
  if(isset($dashboard_data['fact_cards'])){
    ?>
    <div class="row mb-4">
      <div class="col-6 col-md-3 mb-3">
        <a href="<?=base_url('admin/owners')?>" class="text-decoration-none">
          <div class="dashboard-card" data-color="primary">
            <div class="dashboard-card-icon">
              <i class="fas fa-user-tie"></i>
            </div>
            <div class="dashboard-card-content">
              <div class="dashboard-card-value"><?=$dashboard_data['fact_cards']['owners']?></div>
              <div class="dashboard-card-label">Total Owners</div>
            </div>
          </div>
        </a>
      </div>
      <div class="col-6 col-md-3 mb-3">
        <a href="<?=base_url('admin/buildings')?>" class="text-decoration-none">
          <div class="dashboard-card" data-color="success">
            <div class="dashboard-card-icon">
              <i class="fas fa-building"></i>
            </div>
            <div class="dashboard-card-content">
              <div class="dashboard-card-value"><?=$dashboard_data['fact_cards']['buildings']?></div>
              <div class="dashboard-card-label">Buildings</div>
            </div>
          </div>
        </a>
      </div>
      <div class="col-6 col-md-3 mb-3">
        <a href="<?=base_url('admin/agents')?>" class="text-decoration-none">
          <div class="dashboard-card" data-color="info">
            <div class="dashboard-card-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="dashboard-card-content">
              <div class="dashboard-card-value"><?=$dashboard_data['fact_cards']['agents']?></div>
              <div class="dashboard-card-label">Agents</div>
            </div>
          </div>
        </a>
      </div>
      <div class="col-6 col-md-3 mb-3">
        <a href="<?=base_url('admin/tenants')?>" class="text-decoration-none">
          <div class="dashboard-card" data-color="warning">
            <div class="dashboard-card-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="dashboard-card-content">
              <div class="dashboard-card-value"><?=$dashboard_data['fact_cards']['tenants']?></div>
              <div class="dashboard-card-label">Tenants</div>
            </div>
          </div>
        </a>
      </div>
    </div>
    <?php
  }
?>



<div class="mb-4">
  <h5 class="mb-3">Quick Actions</h5>
  <div class="quick-action-container">
    <a href="<?=base_url('admin/owners/add')?>" class="text-decoration-none">
      <div class="quick-action-item" data-color="primary">
        <div class="quick-action-icon">
          <i class="fas fa-user-plus"></i>
        </div>
        <p class="quick-action-label">Add Owner</p>
      </div>
    </a>
    <a href="<?=base_url('admin/buildings/add')?>" class="text-decoration-none">
      <div class="quick-action-item" data-color="success">
        <div class="quick-action-icon">
          <i class="fas fa-plus-circle"></i>
        </div>
        <p class="quick-action-label">Add Building</p>
      </div>
    </a>
    <a href="<?=base_url('admin/reports') ?>" class="text-decoration-none">
      <div class="quick-action-item" data-color="warning">
        <div class="quick-action-icon">
          <i class="fas fa-clipboard-list"></i>
        </div>
        <p class="quick-action-label">Reports</p>
      </div>
    </a>
    <a href="<?=base_url('admin/reports') ?>" class="text-decoration-none">
      <div class="quick-action-item" data-color="info">
        <div class="quick-action-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <p class="quick-action-label">Reports</p>
      </div>
    </a>
  </div>
</div>

<div class="row">
  <div class="col-md-4 mb-4">
    <h5 class="mb-3">Alerts</h5>
    <div class="alert-card" data-priority="high">
      <div class="alert-card-header">
        <div class="alert-card-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="alert-card-time">2h ago</div>
      </div>
      <h6 class="alert-card-title">Urgent Maintenance Request</h6>
      <p class="alert-card-subtitle">Water leak in Building A, Unit 304</p>
    </div>
    <div class="alert-card" data-priority="medium">
      <div class="alert-card-header">
        <div class="alert-card-icon">
          <i class="fas fa-bell"></i>
        </div>
        <div class="alert-card-time">5h ago</div>
      </div>
      <h6 class="alert-card-title">Rent Payment Overdue</h6>
      <p class="alert-card-subtitle">3 tenants have overdue payments</p>
    </div>
    <div class="alert-card" data-priority="low">
      <div class="alert-card-header">
        <div class="alert-card-icon">
          <i class="fas fa-info-circle"></i>
        </div>
        <div class="alert-card-time">1d ago</div>
      </div>
      <h6 class="alert-card-title">New Agent Registration</h6>
      <p class="alert-card-subtitle">2 new agents awaiting approval</p>
    </div>
  </div>

  <!-- Recent Activity Column -->
  <div class="col-md-8">
    <div class="activity-card">
      <div class="activity-card-header">
        <h5 class="activity-card-title">Recent Activity</h5>
        <a href="activity-log.php" class="activity-card-link">View All</a>
      </div>
      <ul class="activity-list">
        <li class="activity-item">
          <div class="activity-icon bg-primary">
            <i class="fas fa-user-plus"></i>
          </div>
          <div class="activity-content">
            <h6 class="activity-title">New Agent Onboarded</h6>
            <p class="activity-subtitle">John Doe was added as an agent</p>
          </div>
          <div class="activity-time">2h ago</div>
        </li>
        <li class="activity-item">
          <div class="activity-icon bg-success">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="activity-content">
            <h6 class="activity-title">Maintenance Request Completed</h6>
            <p class="activity-subtitle">Plumbing issue in Building C resolved</p>
          </div>
          <div class="activity-time">5h ago</div>
        </li>
        <li class="activity-item">
          <div class="activity-icon bg-warning">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="activity-content">
            <h6 class="activity-title">New Maintenance Request</h6>
            <p class="activity-subtitle">AC not working in Building B, Unit 205</p>
          </div>
          <div class="activity-time">8h ago</div>
        </li>
        <li class="activity-item">
          <div class="activity-icon bg-info">
            <i class="fas fa-building"></i>
          </div>
          <div class="activity-content">
            <h6 class="activity-title">New Building Added</h6>
            <p class="activity-subtitle">Skyline Towers added to the system</p>
          </div>
          <div class="activity-time">1d ago</div>
        </li>
        <li class="activity-item">
          <div class="activity-icon bg-primary">
            <i class="fas fa-file-signature"></i>
          </div>
          <div class="activity-content">
            <h6 class="activity-title">Lease Agreement Signed</h6>
            <p class="activity-subtitle">New tenant in Building A, Unit 102</p>
          </div>
          <div class="activity-time">2d ago</div>
        </li>
      </ul>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="maintenance-card">
      <div class="maintenance-card-header">
        <h5 class="maintenance-card-title">Recent Maintenance Requests</h5>
        <a href="maintenance-requests.php" class="maintenance-card-link">View All</a>
      </div>
      <div class="card-body p-0">
        <div class="list-group list-group-flush">
          <div class="list-group-item py-2">
            <div class="d-flex justify-content-between mb-1">
              <span class="badge bg-primary" style="font-size: 0.65rem;">#MT-1234</span>
              <span class="badge bg-danger" style="font-size: 0.65rem;">Urgent</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span class="small fw-medium">Skyline Towers</span>
              <span class="text-muted" style="font-size: 0.7rem;">2d ago</span>
            </div>
            <p class="mb-1" style="font-size: 0.7rem;">Water leakage from ceiling</p>
            <p class="mb-0 text-muted" style="font-size: 0.7rem;">Unit #304</p>
          </div>

          <div class="list-group-item py-2">
            <div class="d-flex justify-content-between mb-1">
              <span class="badge bg-primary" style="font-size: 0.65rem;">#MT-1233</span>
              <span class="badge bg-warning" style="font-size: 0.65rem;">In Progress</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span class="small fw-medium">Green Gardens</span>
              <span class="text-muted" style="font-size: 0.7rem;">3d ago</span>
            </div>
            <p class="mb-1" style="font-size: 0.7rem;">Electrical issues in kitchen</p>
            <p class="mb-0 text-muted" style="font-size: 0.7rem;">Unit #102</p>
          </div>

          <div class="list-group-item py-2">
            <div class="d-flex justify-content-between mb-1">
              <span class="badge bg-primary" style="font-size: 0.65rem;">#MT-1232</span>
              <span class="badge bg-success" style="font-size: 0.65rem;">Resolved</span>
            </div>
            <div class="d-flex justify-content-between mb-1">
              <span class="small fw-medium">Urban Heights</span>
              <span class="text-muted" style="font-size: 0.7rem;">5d ago</span>
            </div>
            <p class="mb-1" style="font-size: 0.7rem;">Elevator malfunction</p>
            <p class="mb-0 text-muted" style="font-size: 0.7rem;">Common Area</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<?php
// Custom scripts specific to this page
$customScripts = <<<EOT
// Add touch-friendly interactions for dashboard cards
$('.dashboard-card').on('click', function(e) {
  // The click event is now handled by the parent <a> tag
  // We just add the animation effect here
  const iconContainer = $(this).find('.dashboard-card-icon');
  
  // Add pulse animation to the icon
  iconContainer.css('animation', 'pulse 0.6s ease');
  
  // Remove animation after it completes
  setTimeout(function() {
    iconContainer.css('animation', '');
  }, 600);
});

// Add touch-friendly interactions for quick action items
$('.quick-action-item').on('click', function(e) {
  // The click event is now handled by the parent <a> tag
  // We just add the animation effect here
  const iconContainer = $(this).find('.quick-action-icon');
  
  // Add pulse animation to the icon
  iconContainer.css('animation', 'pulse 0.6s ease');
  
  // Remove animation after it completes
  setTimeout(function() {
    iconContainer.css('animation', '');
  }, 600);
});

// Add touch-friendly interactions for alert cards
$('.alert-card').on('click', function() {
  const title = $(this).find('.alert-card-title').text();
  const priority = $(this).attr('data-priority');
  
  // Add subtle animation
  $(this).css('transform', 'scale(0.98)');
  
  // Remove animation after it completes
  setTimeout(function() {
    $('.alert-card').css('transform', '');
  }, 300);
  
  // Show alert after a small delay
  setTimeout(function() {
    alert('Viewing alert: ' + title + ' (Priority: ' + priority + ')');
  }, 300);
});
EOT;

?>
