<section class="content" style="max-width:1400px">
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Create Owner</h5>
                <p class="card-subtitle mb-2 text-muted">Fill in the form below to create a new owner.</p>
                <a href="<?= base_url('admin/owners') ?>" class="btn btn-rounded btn-outline-secondary float-end">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
        </div>
        <!-- Onboarding Form Card -->
        <div class="card">
            <div class="card-body">
            <!-- Step Progress Indicator -->
            <div class="step-progress">
                <div class="step active" data-step="1">
                <div class="step-icon">1</div>
                <div class="step-text">Basic Info</div>
                </div>
                <div class="step" data-step="2">
                <div class="step-icon">2</div>
                <div class="step-text">Contact</div>
                </div>
                <div class="step" data-step="3">
                <div class="step-icon">3</div>
                <div class="step-text">Verification</div>
                </div>
            </div>

            <!-- Onboarding Form -->
            <form id="agentOnboardingForm">
                <!-- Step 1: Basic Information -->
                <div class="form-step active" id="step1">
                <h5 class="mb-4">Basic Information</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="fullName" class="form-label">Full Name*</label>
                    <input type="text" class="form-control" id="fullName" placeholder="Enter your full name" required>
                    </div>
                    <div class="col-md-6">
                    <label for="email" class="form-label">Email Address*</label>
                    <input type="email" class="form-control" id="email" placeholder="Enter your email" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="phone" class="form-label">Phone Number*</label>
                    <input type="tel" class="form-control" id="phone" placeholder="Enter your phone number" required>
                    </div>
                    <div class="col-md-6">
                    <label for="agentType" class="form-label">I am a*</label>
                    <select class="form-control" id="agentType" required>
                        <option value="">Select Type</option>
                        <option value="agent">Property Agent</option>
                        <option value="building_owner">Building Owner</option>
                        <option value="both">Both Agent and Building Owner</option>
                    </select>
                    <div class="form-text mt-2">
                        <small>
                        <strong>Property Agent:</strong> Manages day-to-day operations for building owners<br>
                        <strong>Building Owner:</strong> Owns buildings and assigns agents to manage them<br>
                        <strong>Both:</strong> Owns buildings and also works as an agent for other owners
                        </small>
                    </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                    <label for="companyName" class="form-label">Company Name (if applicable)</label>
                    <input type="text" class="form-control" id="companyName" placeholder="Enter company name">
                    </div>
                </div>
                <div class="form-navigation">
                    <div></div> <!-- Empty div for spacing -->
                    <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                </div>
                </div>

                <!-- Step 2: Contact Information -->
                <div class="form-step" id="step2">
                <h5 class="mb-4">Contact Information</h5>
                <div class="row mb-3">
                    <div class="col-md-12">
                    <label for="address" class="form-label">Address*</label>
                    <input type="text" class="form-control mb-3" id="address" placeholder="Street Address" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="city" class="form-label">City*</label>
                    <input type="text" class="form-control" id="city" required>
                    </div>
                    <div class="col-md-6">
                    <label for="state" class="form-label">State/Province*</label>
                    <input type="text" class="form-control" id="state" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="zipCode" class="form-label">Zip/Postal Code*</label>
                    <input type="text" class="form-control" id="zipCode" required>
                    </div>
                    <div class="col-md-6">
                    <label for="country" class="form-label">Country*</label>
                    <select class="form-control" id="country" required>
                        <option value="">Select Country</option>
                        <option value="us">United States</option>
                        <option value="ca">Canada</option>
                        <option value="uk">United Kingdom</option>
                    </select>
                    </div>
                </div>
                <div class="form-navigation">
                    <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i> Previous</button>
                    <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                </div>
                </div>

                <!-- Step 3: Verification -->
                <div class="form-step" id="step3">
                <h5 class="mb-4">Verification</h5>
                <div class="row mb-4">
                    <div class="col-md-12">
                    <label class="form-label">ID Proof*</label>
                    <div class="file-upload-wrapper mb-2">
                        <input type="file" id="idProof" accept="image/*,.pdf" required>
                        <div class="file-upload-content">
                        <div class="file-upload-icon">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <div class="file-upload-text">
                            Click or drag to upload ID proof
                        </div>
                        </div>
                    </div>
                    <div class="form-text">Upload a government-issued ID. Accepted formats: JPG, PNG, PDF. Max size: 5MB.</div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-12">
                    <div class="card bg-light">
                        <div class="card-body" style="max-height: 150px; overflow-y: auto;">
                        <h6>Terms of Service</h6>
                        <p>By submitting this form, you agree to the THREDEX platform terms and conditions. You confirm that all information provided is accurate and complete. You understand that THREDEX may verify your information and may reject your application at its discretion.</p>
                        </div>
                    </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-12">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="termsAgree" required>
                        <label class="form-check-label" for="termsAgree">
                        I agree to the Terms of Service and Privacy Policy*
                        </label>
                    </div>
                    </div>
                </div>
                <div class="form-navigation">
                    <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i> Previous</button>
                    <button type="submit" class="btn btn-success">Submit Application</button>
                </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</section>


<script>
// Building card hover effect
$('.building-card').hover(
  function() {
    $(this).find('.building-card-header').css('transform', 'scale(1.02)');
  },
  function() {
    $(this).find('.building-card-header').css('transform', 'scale(1)');
  }
);

// Building action buttons
$('.building-card-action').on('click', function() {
  const action = $(this).text().trim();
  const buildingName = $(this).closest('.building-card').find('.building-card-title').text();
  
  if (action.includes('Delete')) {
    if (confirm('Are you sure you want to delete ' + buildingName + '?')) {
      alert('Building ' + buildingName + ' has been deleted');
    }
  } else if (action.includes('Edit')) {
    alert('Editing building: ' + buildingName);
  } else if (action.includes('View')) {
    alert('Viewing building: ' + buildingName);
  }
});

// Filter functionality
$('.filter-btn').on('click', function() {
  const searchTerm = $('.search-input input').val().toLowerCase();
  const statusFilter = $('.filter-select').eq(0).val();
  
  alert('Filtering buildings with search term: "' + searchTerm + '" and status: "' + statusFilter + '"');
});

// Placeholder for map initialization
// In a real application, this would initialize a map with building locations
setTimeout(function() {
  $('.map-placeholder').html('<i class="fas fa-map-marker-alt"></i><p>Map loaded successfully</p>');
}, 1500);
</script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    const steps = document.querySelectorAll('.form-step');
    const stepIndicators = document.querySelectorAll('.step');
    let currentStep = 0;

    function showStep(index) {
        steps.forEach((step, i) => {
            step.classList.toggle('active', i === index);
            stepIndicators[i].classList.toggle('active', i === index);
        });
        currentStep = index;
    }

    document.querySelectorAll('.next-step').forEach(btn => {
        btn.addEventListener('click', () => {
            if (currentStep < steps.length - 1) {
                showStep(currentStep + 1);
            }
        });
    });

    document.querySelectorAll('.prev-step').forEach(btn => {
        btn.addEventListener('click', () => {
            if (currentStep > 0) {
                showStep(currentStep - 1);
            }
        });
    });

    // Initial step display
    showStep(currentStep);
});
</script>

