<section class="content" style="max-width:1400px">
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Create Owner</h5>
                <p class="card-subtitle mb-2 text-muted">Fill in the form below to create a new owner.</p>
                <a href="<?= base_url('admin/owners') ?>" class="btn btn-rounded btn-outline-secondary float-end">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
        </div>

        <?php if (session()->getFlashdata('message_success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('message_success') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('message_danger')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('message_danger') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Owner Form Card -->
        <div class="card">
            <div class="card-body">
            <!-- Step Progress Indicator -->
            <div class="step-progress">
                <div class="step active" data-step="1">
                <div class="step-icon">1</div>
                <div class="step-text">Basic Info</div>
                </div>
                <div class="step" data-step="2">
                <div class="step-icon">2</div>
                <div class="step-text">Contact</div>
                </div>
                <div class="step" data-step="3">
                <div class="step-icon">3</div>
                <div class="step-text">Business Info</div>
                </div>
            </div>

            <!-- Owner Form -->
            <form method="POST" action="<?= base_url('admin/owners/add') ?>" enctype="multipart/form-data" id="ownerForm">
                <!-- Step 1: Basic Information -->
                <div class="form-step active" id="step1">
                <h5 class="mb-4">Basic Information</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="name" class="form-label">Full Name*</label>
                    <input type="text" class="form-control" name="name" id="name"
                           value="<?= old('name') ?>" placeholder="Enter full name" required>
                    <?php if (session()->getFlashdata('validation_errors')['name'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['name'] ?></div>
                    <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                    <label for="email" class="form-label">Email Address*</label>
                    <input type="email" class="form-control" name="email" id="email"
                           value="<?= old('email') ?>" placeholder="Enter email address" required>
                    <?php if (session()->getFlashdata('validation_errors')['email'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['email'] ?></div>
                    <?php endif; ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="phone" class="form-label">Phone Number*</label>
                    <input type="tel" class="form-control" name="phone" id="phone"
                           value="<?= old('phone') ?>" placeholder="Enter phone number" required>
                    <?php if (session()->getFlashdata('validation_errors')['phone'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['phone'] ?></div>
                    <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                    <label for="country_code" class="form-label">Country Code*</label>
                    <select class="form-control" name="country_code" id="country_code" required>
                        <option value="">Select Country Code</option>
                        <option value="+1" <?= old('country_code') == '+1' ? 'selected' : '' ?>>+1 (US/Canada)</option>
                        <option value="+44" <?= old('country_code') == '+44' ? 'selected' : '' ?>>+44 (UK)</option>
                        <option value="+91" <?= old('country_code') == '+91' ? 'selected' : '' ?>>+91 (India)</option>
                        <option value="+971" <?= old('country_code') == '+971' ? 'selected' : '' ?>>+971 (UAE)</option>
                    </select>
                    <?php if (session()->getFlashdata('validation_errors')['country_code'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['country_code'] ?></div>
                    <?php endif; ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="password" class="form-label">Password*</label>
                    <input type="password" class="form-control" name="password" id="password"
                           placeholder="Enter password (min 8 characters)" required>
                    <?php if (session()->getFlashdata('validation_errors')['password'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['password'] ?></div>
                    <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                    <label for="profile_image" class="form-label">Profile Image</label>
                    <input type="file" class="form-control" name="profile_image" id="profile_image" accept="image/*">
                    <div class="form-text">Upload a profile image (optional)</div>
                    </div>
                </div>
                <div class="form-navigation">
                    <div></div> <!-- Empty div for spacing -->
                    <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                </div>
                </div>

                <!-- Step 2: Contact Information -->
                <div class="form-step" id="step2">
                <h5 class="mb-4">Contact Information</h5>
                <div class="row mb-3">
                    <div class="col-md-12">
                    <label for="address" class="form-label">Address</label>
                    <input type="text" class="form-control mb-3" name="address" id="address"
                           value="<?= old('address') ?>" placeholder="Street Address">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="city" class="form-label">City</label>
                    <input type="text" class="form-control" name="city" id="city"
                           value="<?= old('city') ?>" placeholder="Enter city">
                    </div>
                    <div class="col-md-6">
                    <label for="state" class="form-label">State/Province</label>
                    <input type="text" class="form-control" name="state" id="state"
                           value="<?= old('state') ?>" placeholder="Enter state/province">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="post_code" class="form-label">Zip/Postal Code</label>
                    <input type="text" class="form-control" name="post_code" id="post_code"
                           value="<?= old('post_code') ?>" placeholder="Enter postal code">
                    </div>
                    <div class="col-md-6">
                    <label for="country" class="form-label">Country</label>
                    <select class="form-control" name="country" id="country">
                        <option value="">Select Country</option>
                        <option value="United States" <?= old('country') == 'United States' ? 'selected' : '' ?>>United States</option>
                        <option value="Canada" <?= old('country') == 'Canada' ? 'selected' : '' ?>>Canada</option>
                        <option value="United Kingdom" <?= old('country') == 'United Kingdom' ? 'selected' : '' ?>>United Kingdom</option>
                        <option value="India" <?= old('country') == 'India' ? 'selected' : '' ?>>India</option>
                        <option value="UAE" <?= old('country') == 'UAE' ? 'selected' : '' ?>>UAE</option>
                    </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="phone_secondary" class="form-label">Secondary Phone</label>
                    <input type="tel" class="form-control" name="phone_secondary" id="phone_secondary"
                           value="<?= old('phone_secondary') ?>" placeholder="Enter secondary phone (optional)">
                    </div>
                </div>
                <div class="form-navigation">
                    <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i> Previous</button>
                    <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                </div>
                </div>

                <!-- Step 3: Verification -->
                <div class="form-step" id="step3">
                <h5 class="mb-4">Verification</h5>
                <div class="row mb-4">
                    <div class="col-md-12">
                    <label class="form-label">ID Proof*</label>
                    <div class="file-upload-wrapper mb-2">
                        <input type="file" id="idProof" accept="image/*,.pdf" required>
                        <div class="file-upload-content">
                        <div class="file-upload-icon">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <div class="file-upload-text">
                            Click or drag to upload ID proof
                        </div>
                        </div>
                    </div>
                    <div class="form-text">Upload a government-issued ID. Accepted formats: JPG, PNG, PDF. Max size: 5MB.</div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-12">
                    <div class="card bg-light">
                        <div class="card-body" style="max-height: 150px; overflow-y: auto;">
                        <h6>Terms of Service</h6>
                        <p>By submitting this form, you agree to the THREDEX platform terms and conditions. You confirm that all information provided is accurate and complete. You understand that THREDEX may verify your information and may reject your application at its discretion.</p>
                        </div>
                    </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-12">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="termsAgree" required>
                        <label class="form-check-label" for="termsAgree">
                        I agree to the Terms of Service and Privacy Policy*
                        </label>
                    </div>
                    </div>
                </div>
                <div class="form-navigation">
                    <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i> Previous</button>
                    <button type="submit" class="btn btn-success">Submit Application</button>
                </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</section>


<script>
// Building card hover effect
$('.building-card').hover(
  function() {
    $(this).find('.building-card-header').css('transform', 'scale(1.02)');
  },
  function() {
    $(this).find('.building-card-header').css('transform', 'scale(1)');
  }
);

// Building action buttons
$('.building-card-action').on('click', function() {
  const action = $(this).text().trim();
  const buildingName = $(this).closest('.building-card').find('.building-card-title').text();
  
  if (action.includes('Delete')) {
    if (confirm('Are you sure you want to delete ' + buildingName + '?')) {
      alert('Building ' + buildingName + ' has been deleted');
    }
  } else if (action.includes('Edit')) {
    alert('Editing building: ' + buildingName);
  } else if (action.includes('View')) {
    alert('Viewing building: ' + buildingName);
  }
});

// Filter functionality
$('.filter-btn').on('click', function() {
  const searchTerm = $('.search-input input').val().toLowerCase();
  const statusFilter = $('.filter-select').eq(0).val();
  
  alert('Filtering buildings with search term: "' + searchTerm + '" and status: "' + statusFilter + '"');
});

// Placeholder for map initialization
// In a real application, this would initialize a map with building locations
setTimeout(function() {
  $('.map-placeholder').html('<i class="fas fa-map-marker-alt"></i><p>Map loaded successfully</p>');
}, 1500);
</script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    const steps = document.querySelectorAll('.form-step');
    const stepIndicators = document.querySelectorAll('.step');
    let currentStep = 0;

    function showStep(index) {
        steps.forEach((step, i) => {
            step.classList.toggle('active', i === index);
            stepIndicators[i].classList.toggle('active', i === index);
        });
        currentStep = index;
    }

    document.querySelectorAll('.next-step').forEach(btn => {
        btn.addEventListener('click', () => {
            if (currentStep < steps.length - 1) {
                showStep(currentStep + 1);
            }
        });
    });

    document.querySelectorAll('.prev-step').forEach(btn => {
        btn.addEventListener('click', () => {
            if (currentStep > 0) {
                showStep(currentStep - 1);
            }
        });
    });

    // Initial step display
    showStep(currentStep);
});
</script>

