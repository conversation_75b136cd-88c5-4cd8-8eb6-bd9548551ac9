<section class="content" style="max-width:1400px">
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="card-title mb-1">Edit Owner</h5>
                        <p class="card-subtitle text-muted">Update the owner information below.</p>
                    </div>
                    <a href="<?= base_url('admin/owners') ?>" class="btn btn-rounded btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back
                    </a>
                </div>
            </div>
        </div>

        <?php if (session()->getFlashdata('message_success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('message_success') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('message_danger')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= session()->getFlashdata('message_danger') ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Owner Edit Form Card -->
        <div class="card">
            <div class="card-body">

            <style>
            /* Step Progress Styles */
            .step-progress {
                display: flex;
                justify-content: center;
                margin-bottom: 30px;
                position: relative;
            }

            .step {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
                flex: 1;
                max-width: 200px;
            }

            .step:not(:last-child)::after {
                content: '';
                position: absolute;
                top: 20px;
                left: 60%;
                width: 80%;
                height: 2px;
                background-color: #e0e0e0;
                z-index: 1;
            }

            .step.active:not(:last-child)::after {
                background-color: var(--primary-color);
            }

            .step-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-color: #e0e0e0;
                color: #666;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                margin-bottom: 8px;
                position: relative;
                z-index: 2;
                transition: all 0.3s ease;
            }

            .step.active .step-icon {
                background-color: var(--primary-color);
                color: white;
            }

            .step-text {
                font-size: 0.85rem;
                color: #666;
                text-align: center;
                font-weight: 500;
            }

            .step.active .step-text {
                color: var(--primary-color);
                font-weight: 600;
            }

            /* Form Step Styles */
            .form-step {
                display: none;
            }

            .form-step.active {
                display: block;
            }

            .form-navigation {
                display: flex;
                justify-content: space-between;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e0e0e0;
            }
            </style>
            <!-- Step Progress Indicator -->
            <div class="step-progress">
                <div class="step active" data-step="1">
                <div class="step-icon">1</div>
                <div class="step-text">Basic Info</div>
                </div>
                <div class="step" data-step="2">
                <div class="step-icon">2</div>
                <div class="step-text">Contact</div>
                </div>
                <div class="step" data-step="3">
                <div class="step-icon">3</div>
                <div class="step-text">Business Info</div>
                </div>
            </div>

            <!-- Owner Edit Form -->
            <form method="POST" action="<?= base_url('admin/owners/edit/' . $edit_data['id']) ?>" enctype="multipart/form-data" id="ownerEditForm">
                <!-- Step 1: Basic Information -->
                <div class="form-step active" id="step1">
                <h5 class="mb-4">Basic Information</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="name" class="form-label">Full Name*</label>
                    <input type="text" class="form-control" name="name" id="name"
                           value="<?= old('name', $edit_data['name']) ?>" placeholder="Enter full name" required>
                    <?php if (session()->getFlashdata('validation_errors')['name'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['name'] ?></div>
                    <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                    <label for="email" class="form-label">Email Address*</label>
                    <input type="email" class="form-control" name="email" id="email"
                           value="<?= old('email', $edit_data['email']) ?>" placeholder="Enter email address" required>
                    <?php if (session()->getFlashdata('validation_errors')['email'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['email'] ?></div>
                    <?php endif; ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="phone" class="form-label">Phone Number*</label>
                    <input type="tel" class="form-control" name="phone" id="phone"
                           value="<?= old('phone', $edit_data['phone']) ?>" placeholder="Enter phone number" required>
                    <?php if (session()->getFlashdata('validation_errors')['phone'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['phone'] ?></div>
                    <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                    <label for="country_code" class="form-label">Country Code*</label>
                    <select class="form-control" name="country_code" id="country_code" required>
                        <option value="">Select Country Code</option>
                        <option value="+1" <?= old('country_code', $edit_data['country_code']) == '+1' ? 'selected' : '' ?>>+1 (US/Canada)</option>
                        <option value="+44" <?= old('country_code', $edit_data['country_code']) == '+44' ? 'selected' : '' ?>>+44 (UK)</option>
                        <option value="+91" <?= old('country_code', $edit_data['country_code']) == '+91' ? 'selected' : '' ?>>+91 (India)</option>
                        <option value="+971" <?= old('country_code', $edit_data['country_code']) == '+971' ? 'selected' : '' ?>>+971 (UAE)</option>
                    </select>
                    <?php if (session()->getFlashdata('validation_errors')['country_code'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['country_code'] ?></div>
                    <?php endif; ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" name="password" id="password"
                           placeholder="Leave blank to keep current password">
                    <div class="form-text">Leave blank to keep current password</div>
                    <?php if (session()->getFlashdata('validation_errors')['password'] ?? false): ?>
                        <div class="text-danger small"><?= session()->getFlashdata('validation_errors')['password'] ?></div>
                    <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-control" name="status" id="status">
                        <option value="1" <?= old('status', $edit_data['status']) == '1' ? 'selected' : '' ?>>Active</option>
                        <option value="0" <?= old('status', $edit_data['status']) == '0' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="is_blocked" class="form-label">Blocked Status</label>
                    <select class="form-control" name="is_blocked" id="is_blocked">
                        <option value="0" <?= old('is_blocked', $edit_data['is_blocked']) == '0' ? 'selected' : '' ?>>Not Blocked</option>
                        <option value="1" <?= old('is_blocked', $edit_data['is_blocked']) == '1' ? 'selected' : '' ?>>Blocked</option>
                    </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="profile_image" class="form-label">Profile Image</label>
                    <input type="file" class="form-control" name="profile_image" id="profile_image" accept="image/*">
                    <div class="form-text">Upload a new profile image (optional)</div>
                    </div>
                    <div class="col-md-6">
                    <?php if (!empty($edit_data['profile_image'])): ?>
                        <label class="form-label">Current Image</label><br>
                        <img src="<?= base_url($edit_data['profile_image']) ?>" alt="Current Profile" class="img-thumbnail" style="max-width: 100px;">
                    <?php endif; ?>
                    </div>
                </div>
                <div class="form-navigation">
                    <div></div> <!-- Empty div for spacing -->
                    <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                </div>
                </div>

                <!-- Step 2: Contact Information -->
                <div class="form-step" id="step2">
                <h5 class="mb-4">Contact Information</h5>
                <div class="row mb-3">
                    <div class="col-md-12">
                    <label for="address" class="form-label">Address</label>
                    <input type="text" class="form-control mb-3" name="address" id="address"
                           value="<?= old('address', $edit_data['address']) ?>" placeholder="Street Address">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="city" class="form-label">City</label>
                    <input type="text" class="form-control" name="city" id="city"
                           value="<?= old('city', $edit_data['city']) ?>" placeholder="Enter city">
                    </div>
                    <div class="col-md-6">
                    <label for="state" class="form-label">State/Province</label>
                    <input type="text" class="form-control" name="state" id="state"
                           value="<?= old('state', $edit_data['state']) ?>" placeholder="Enter state/province">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="post_code" class="form-label">Zip/Postal Code</label>
                    <input type="text" class="form-control" name="post_code" id="post_code"
                           value="<?= old('post_code', $edit_data['post_code']) ?>" placeholder="Enter postal code">
                    </div>
                    <div class="col-md-6">
                    <label for="country" class="form-label">Country</label>
                    <select class="form-control" name="country" id="country">
                        <option value="">Select Country</option>
                        <option value="United States" <?= old('country', $edit_data['country']) == 'United States' ? 'selected' : '' ?>>United States</option>
                        <option value="Canada" <?= old('country', $edit_data['country']) == 'Canada' ? 'selected' : '' ?>>Canada</option>
                        <option value="United Kingdom" <?= old('country', $edit_data['country']) == 'United Kingdom' ? 'selected' : '' ?>>United Kingdom</option>
                        <option value="India" <?= old('country', $edit_data['country']) == 'India' ? 'selected' : '' ?>>India</option>
                        <option value="UAE" <?= old('country', $edit_data['country']) == 'UAE' ? 'selected' : '' ?>>UAE</option>
                    </select>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="phone_secondary" class="form-label">Secondary Phone</label>
                    <input type="tel" class="form-control" name="phone_secondary" id="phone_secondary"
                           value="<?= old('phone_secondary', $edit_data['phone_secondary']) ?>" placeholder="Enter secondary phone (optional)">
                    </div>
                </div>
                <div class="form-navigation">
                    <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i> Previous</button>
                    <button type="button" class="btn btn-primary next-step">Next <i class="fas fa-arrow-right ms-2"></i></button>
                </div>
                </div>

                <!-- Step 3: Business Information -->
                <div class="form-step" id="step3">
                <h5 class="mb-4">Business Information</h5>
                <div class="row mb-3">
                    <div class="col-md-6">
                    <label for="business_name" class="form-label">Business Name</label>
                    <input type="text" class="form-control" name="business_name" id="business_name"
                           value="<?= old('business_name', $edit_data['business_name']) ?>" placeholder="Enter business name (optional)">
                    </div>
                    <div class="col-md-6">
                    <label for="tax_number" class="form-label">Tax Number</label>
                    <input type="text" class="form-control" name="tax_number" id="tax_number"
                           value="<?= old('tax_number', $edit_data['tax_number']) ?>" placeholder="Enter tax number (optional)">
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-12">
                    <div class="card bg-light">
                        <div class="card-body">
                        <h6>Owner Account Information</h6>
                        <p>Update the owner information. Changes will be reflected immediately in the system. All information provided will be kept confidential and used only for property management purposes.</p>
                        </div>
                    </div>
                    </div>
                </div>
                <div class="form-navigation">
                    <button type="button" class="btn btn-outline-secondary prev-step"><i class="fas fa-arrow-left me-2"></i> Previous</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Update Owner
                    </button>
                </div>
                </div>
            </form>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function () {
    const steps = document.querySelectorAll('.form-step');
    const stepIndicators = document.querySelectorAll('.step');
    let currentStep = 0;

    function showStep(index) {
        steps.forEach((step, i) => {
            step.classList.toggle('active', i === index);
            stepIndicators[i].classList.toggle('active', i === index);
        });
        currentStep = index;
    }

    document.querySelectorAll('.next-step').forEach(btn => {
        btn.addEventListener('click', () => {
            if (currentStep < steps.length - 1) {
                showStep(currentStep + 1);
            }
        });
    });

    document.querySelectorAll('.prev-step').forEach(btn => {
        btn.addEventListener('click', () => {
            if (currentStep > 0) {
                showStep(currentStep - 1);
            }
        });
    });

    // Initial step display
    showStep(currentStep);
});
</script>