<?php if ($total_pages > 1): ?>
<nav aria-label="Owners pagination" class="mt-4">
  <ul class="pagination justify-content-center">
    <?php if ($current_page > 1): ?>
      <li class="page-item">
        <a class="page-link pagination-link" href="#" data-page="<?= $current_page - 1 ?>">Previous</a>
      </li>
    <?php else: ?>
      <li class="page-item disabled">
        <span class="page-link">Previous</span>
      </li>
    <?php endif; ?>
    
    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
      <?php if ($i == $current_page): ?>
        <li class="page-item active">
          <span class="page-link"><?= $i ?></span>
        </li>
      <?php else: ?>
        <li class="page-item">
          <a class="page-link pagination-link" href="#" data-page="<?= $i ?>"><?= $i ?></a>
        </li>
      <?php endif; ?>
    <?php endfor; ?>
    
    <?php if ($current_page < $total_pages): ?>
      <li class="page-item">
        <a class="page-link pagination-link" href="#" data-page="<?= $current_page + 1 ?>">Next</a>
      </li>
    <?php else: ?>
      <li class="page-item disabled">
        <span class="page-link">Next</span>
      </li>
    <?php endif; ?>
  </ul>
</nav>
<?php endif; ?>
