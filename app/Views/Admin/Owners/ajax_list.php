<!-- Owners Grid -->
<div class="row" id="ownersList">
  <?php if (!empty($list_items)): ?>
    <?php foreach ($list_items as $owner): ?>
      <div class="col-md-6 col-lg-4">
        <div class="entity-card">
          <div class="entity-card-header">
            <div class="entity-avatar">
              <?php if (!empty($owner['profile_image'])): ?>
                <img src="<?= base_url($owner['profile_image']) ?>" alt="Owner Avatar">
              <?php else: ?>
                <img src="https://web.trogon.info/thredex/super_admin/images/dummy.png" alt="Default Avatar">
              <?php endif; ?>
            </div>
          </div>
          <div class="entity-card-body">
            <h5 class="entity-name"><?= esc($owner['name']) ?></h5>
            <p class="entity-title">
              <?= !empty($owner['business_name']) ? esc($owner['business_name']) : 'Property Owner' ?>
              <span class="status-badge <?= $owner['status'] == 1 ? 'status-active' : 'status-inactive' ?> float-end">
                <?= $owner['status'] == 1 ? 'Active' : 'Inactive' ?>
              </span>
              <?php if ($owner['is_blocked'] == 1): ?>
                <span class="status-badge" style="background-color: rgba(220, 53, 69, 0.1); color: #dc3545;">Blocked</span>
              <?php endif; ?>
            </p>
            
            <div class="entity-contact mb-3">
              <div class="entity-contact-item">
                <i class="fas fa-envelope"></i>
                <?= esc($owner['email']) ?>
              </div>
              <div class="entity-contact-item">
                <i class="fas fa-phone"></i>
                <?= esc($owner['phone']) ?>
              </div>
              <?php if (!empty($owner['city'])): ?>
                <div class="entity-contact-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <?= esc($owner['city']) ?><?= !empty($owner['state']) ? ', ' . esc($owner['state']) : '' ?>
                </div>
              <?php endif; ?>
              <div class="entity-contact-item">
                <i class="fas fa-calendar"></i>
                <?= date('M d, Y', strtotime($owner['created_at'])) ?>
              </div>
            </div>
            
            <div class="entity-actions">
              <a href="<?= base_url('admin/owners/edit/' . $owner['id']) ?>" class="entity-action">Edit</a>
              <a href="<?= base_url('admin/owners/delete/' . $owner['id']) ?>" 
                 class="entity-action delete" 
                 onclick="return confirm('Are you sure you want to delete this owner?')">Delete</a>
            </div>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  <?php else: ?>
    <div class="col-12">
      <div class="text-center py-5">
        <i class="fas fa-users fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No owners found</h5>
        <p class="text-muted">Try adjusting your filters or add your first owner</p>
        <a href="<?= base_url('admin/owners/add') ?>" class="btn btn-primary">
          <i class="fas fa-plus"></i> Add Owner
        </a>
      </div>
    </div>
  <?php endif; ?>
</div>
