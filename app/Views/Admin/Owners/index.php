<?php
$showFab = true;
$fabLink = base_url('admin/owners/add');
?>
<style>
/* Entity Cards */
.entity-card {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.entity-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.entity-card-header {
  position: relative;
  height: 100px;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.entity-avatar {
  position: absolute;
  bottom: -30px;
  left: 20px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid white;
  background-color: white;
  overflow: hidden;
}

.entity-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-card-body {
  padding: 40px 20px 20px;
}

.agent-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 5px;
}

.agent-title {
  font-size: 0.8rem;
  color: #8898aa;
  margin-bottom: 15px;
}

.agent-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.agent-stat {
  text-align: center;
}

.agent-stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 5px;
}

.agent-stat-label {
  font-size: 0.7rem;
  color: #8898aa;
}

.agent-actions {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 15px;
}

.agent-action {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.agent-action:hover {
  background-color: rgba(86, 100, 210, 0.1);
}

.agent-action.delete {
  color: var(--danger-color);
}

.agent-action.delete:hover {
  background-color: rgba(245, 54, 92, 0.1);
}

/* Entity Contact Styles */
.entity-contact {
  margin-bottom: 20px;
}

.entity-contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.85rem;
  color: #6c757d;
}

.entity-contact-item i {
  width: 16px;
  margin-right: 8px;
  color: var(--primary-color);
}

/* Filter Bar */
.filter-bar {
  background-color: white;
  border-radius: 16px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.search-input {
  position: relative;
}

.search-input input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.875rem;
}

.search-input input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(86, 100, 210, 0.25);
}

.search-input i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #8898aa;
}

.filter-select {
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 10px 15px;
  font-size: 0.875rem;
  width: 100%;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(86, 100, 210, 0.25);
}

.filter-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  width: 100%;
}

.filter-btn:hover {
  background-color: var(--secondary-color);
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(45, 206, 137, 0.1);
  color: var(--success-color);
}

.status-inactive {
  background-color: rgba(251, 99, 64, 0.1);
  color: var(--warning-color);
}

.status-pending {
  background-color: rgba(17, 205, 239, 0.1);
  color: var(--info-color);
}
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="mb-0">Owners Management</h4>
  <a href="<?= base_url('admin/owners/add') ?>" class="btn btn-primary d-none d-md-block">
    <i class="fas fa-plus"></i> Add New Owner
  </a>
</div>

<!-- Filter Bar -->
<div class="filter-bar">
  <form method="GET" action="<?= base_url('admin/owners') ?>">
    <div class="row">
      <div class="col-md-8 mb-3 mb-md-0">
        <div class="search-input">
          <i class="fas fa-search"></i>
          <input type="text" name="search" value="<?= esc($search ?? '') ?>" placeholder="Search owners by name, email, phone, or business name...">
        </div>
      </div>
      <div class="col-md-2 mb-3 mb-md-0">
        <select class="filter-select" name="status">
          <option value="">All Status</option>
          <option value="1" <?= (isset($_GET['status']) && $_GET['status'] == '1') ? 'selected' : '' ?>>Active</option>
          <option value="0" <?= (isset($_GET['status']) && $_GET['status'] == '0') ? 'selected' : '' ?>>Inactive</option>
        </select>
      </div>
      <div class="col-md-2">
        <button type="submit" class="filter-btn">
          <i class="fas fa-search"></i> Search
        </button>
      </div>
    </div>
  </form>
</div>

<!-- Owners Grid -->
<div class="row">
  <?php if (!empty($list_items)): ?>
    <?php foreach ($list_items as $owner): ?>
      <div class="col-md-6 col-lg-4">
        <div class="entity-card">
          <div class="entity-card-header">
            <div class="entity-avatar">
              <?php if (!empty($owner['profile_image'])): ?>
                <img src="<?= base_url($owner['profile_image']) ?>" alt="Owner Avatar">
              <?php else: ?>
                <img src="https://web.trogon.info/thredex/super_admin/images/dummy.png" alt="Default Avatar">
              <?php endif; ?>
            </div>
          </div>
          <div class="entity-card-body">
            <h5 class="entity-name"><?= esc($owner['name']) ?></h5>
            <p class="entity-title">
              <?= !empty($owner['business_name']) ? esc($owner['business_name']) : 'Property Owner' ?>
              <span class="status-badge <?= $owner['status'] == 1 ? 'status-active' : 'status-inactive' ?> float-end">
                <?= $owner['status'] == 1 ? 'Active' : 'Inactive' ?>
              </span>
            </p>

            <div class="entity-contact mb-3">
              <div class="entity-contact-item">
                <i class="fas fa-envelope"></i>
                <?= esc($owner['email']) ?>
              </div>
              <div class="entity-contact-item">
                <i class="fas fa-phone"></i>
                <?= esc($owner['phone']) ?>
              </div>
              <?php if (!empty($owner['city'])): ?>
                <div class="entity-contact-item">
                  <i class="fas fa-map-marker-alt"></i>
                  <?= esc($owner['city']) ?><?= !empty($owner['state']) ? ', ' . esc($owner['state']) : '' ?>
                </div>
              <?php endif; ?>
            </div>

            <div class="entity-actions">
              <a href="<?= base_url('admin/owners/edit/' . $owner['id']) ?>" class="entity-action">Edit</a>
              <a href="<?= base_url('admin/owners/delete/' . $owner['id']) ?>"
                 class="entity-action delete"
                 onclick="return confirm('Are you sure you want to delete this owner?')">Delete</a>
            </div>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  <?php else: ?>
    <div class="col-12">
      <div class="text-center py-5">
        <i class="fas fa-users fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">No owners found</h5>
        <p class="text-muted">Start by adding your first owner</p>
        <a href="<?= base_url('admin/owners/add') ?>" class="btn btn-primary">
          <i class="fas fa-plus"></i> Add Owner
        </a>
      </div>
    </div>
  <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($total_pages > 1): ?>
<nav aria-label="Owners pagination" class="mt-4">
  <ul class="pagination justify-content-center">
    <?php if ($current_page > 1): ?>
      <li class="page-item">
        <a class="page-link" href="<?= base_url('admin/owners?page=' . ($current_page - 1) . ($search ? '&search=' . urlencode($search) : '')) ?>">Previous</a>
      </li>
    <?php else: ?>
      <li class="page-item disabled">
        <span class="page-link">Previous</span>
      </li>
    <?php endif; ?>

    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
      <?php if ($i == $current_page): ?>
        <li class="page-item active">
          <span class="page-link"><?= $i ?></span>
        </li>
      <?php else: ?>
        <li class="page-item">
          <a class="page-link" href="<?= base_url('admin/owners?page=' . $i . ($search ? '&search=' . urlencode($search) : '')) ?>"><?= $i ?></a>
        </li>
      <?php endif; ?>
    <?php endfor; ?>

    <?php if ($current_page < $total_pages): ?>
      <li class="page-item">
        <a class="page-link" href="<?= base_url('admin/owners?page=' . ($current_page + 1) . ($search ? '&search=' . urlencode($search) : '')) ?>">Next</a>
      </li>
    <?php else: ?>
      <li class="page-item disabled">
        <span class="page-link">Next</span>
      </li>
    <?php endif; ?>
  </ul>
</nav>
<?php endif; ?>
<script>
$(document).ready(function() {
    // Entity card hover effect
    $('.entity-card').hover(
        function() {
            $(this).find('.entity-card-header').css('transform', 'scale(1.02)');
        },
        function() {
            $(this).find('.entity-card-header').css('transform', 'scale(1)');
        }
    );
});
</script>