<?php
$showFab = true;
$fabLink = base_url('admin/owners/add');
?>
<style>
/* Agent Cards */
.agent-card {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.agent-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.agent-card-header {
  position: relative;
  height: 100px;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.agent-avatar {
  position: absolute;
  bottom: -30px;
  left: 20px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid white;
  background-color: white;
  overflow: hidden;
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-card-body {
  padding: 40px 20px 20px;
}

.agent-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 5px;
}

.agent-title {
  font-size: 0.8rem;
  color: #8898aa;
  margin-bottom: 15px;
}

.agent-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.agent-stat {
  text-align: center;
}

.agent-stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 5px;
}

.agent-stat-label {
  font-size: 0.7rem;
  color: #8898aa;
}

.agent-actions {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 15px;
}

.agent-action {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.agent-action:hover {
  background-color: rgba(86, 100, 210, 0.1);
}

.agent-action.delete {
  color: var(--danger-color);
}

.agent-action.delete:hover {
  background-color: rgba(245, 54, 92, 0.1);
}

/* Filter Bar */
.filter-bar {
  background-color: white;
  border-radius: 16px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.search-input {
  position: relative;
}

.search-input input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.875rem;
}

.search-input input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(86, 100, 210, 0.25);
}

.search-input i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #8898aa;
}

.filter-select {
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 10px 15px;
  font-size: 0.875rem;
  width: 100%;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(86, 100, 210, 0.25);
}

.filter-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 15px;
  font-weight: 500;
  transition: all 0.3s ease;
  width: 100%;
}

.filter-btn:hover {
  background-color: var(--secondary-color);
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(45, 206, 137, 0.1);
  color: var(--success-color);
}

.status-inactive {
  background-color: rgba(251, 99, 64, 0.1);
  color: var(--warning-color);
}

.status-pending {
  background-color: rgba(17, 205, 239, 0.1);
  color: var(--info-color);
}
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
  <h4 class="mb-0">Agents Management</h4>
  <a href="agent-onboarding.php" class="btn btn-primary d-none d-md-block">
    <i class="fas fa-plus"></i> Add New Agent
  </a>
</div>

<!-- Filter Bar -->
<div class="filter-bar">
  <div class="row">
    <div class="col-md-5 mb-3 mb-md-0">
      <div class="search-input">
        <i class="fas fa-search"></i>
        <input type="text" placeholder="Search agents...">
      </div>
    </div>
    <div class="col-md-3 mb-3 mb-md-0">
      <select class="filter-select">
        <option value="">All Status</option>
        <option value="active">Active</option>
        <option value="inactive">Inactive</option>
        <option value="pending">Pending</option>
      </select>
    </div>
    <div class="col-md-3 mb-3 mb-md-0">
      <select class="filter-select">
        <option value="">All Buildings</option>
        <option value="skyline">Skyline Towers</option>
        <option value="green">Green Gardens</option>
        <option value="urban">Urban Heights</option>
      </select>
    </div>
    <div class="col-md-1">
      <button class="filter-btn">
        <i class="fas fa-filter"></i>
      </button>
    </div>
  </div>
</div>

<!-- Agents Grid -->
<div class="row">
  <!-- Agent Card 1 -->
  <div class="col-md-6 col-lg-4">
    <div class="agent-card">
      <div class="agent-card-header">
        <div class="agent-avatar">
          <img src="https://web.trogon.info/thredex/super_admin/images/dummy.png" alt="Agent Avatar">
        </div>
      </div>
      <div class="agent-card-body">
        <h5 class="agent-name">John Doe</h5>
        <p class="agent-title">
          Senior Property Manager
          <span class="status-badge status-active float-end">Active</span>
        </p>
        <div class="agent-stats">
          <div class="agent-stat">
            <div class="agent-stat-value">12</div>
            <div class="agent-stat-label">Buildings</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">45</div>
            <div class="agent-stat-label">Tenants</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">8</div>
            <div class="agent-stat-label">Years</div>
          </div>
        </div>
        <div class="agent-actions">
          <button class="agent-action">
            <i class="fas fa-eye"></i> View
          </button>
          <button class="agent-action">
            <i class="fas fa-edit"></i> Edit
          </button>
          <button class="agent-action delete">
            <i class="fas fa-trash"></i> Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Agent Card 2 -->
  <div class="col-md-6 col-lg-4">
    <div class="agent-card">
      <div class="agent-card-header">
        <div class="agent-avatar">
          <img src="https://web.trogon.info/thredex/super_admin/images/dummy.png" alt="Agent Avatar">
        </div>
      </div>
      <div class="agent-card-body">
        <h5 class="agent-name">Jane Smith</h5>
        <p class="agent-title">
          Property Manager
          <span class="status-badge status-active float-end">Active</span>
        </p>
        <div class="agent-stats">
          <div class="agent-stat">
            <div class="agent-stat-value">8</div>
            <div class="agent-stat-label">Buildings</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">32</div>
            <div class="agent-stat-label">Tenants</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">5</div>
            <div class="agent-stat-label">Years</div>
          </div>
        </div>
        <div class="agent-actions">
          <button class="agent-action">
            <i class="fas fa-eye"></i> View
          </button>
          <button class="agent-action">
            <i class="fas fa-edit"></i> Edit
          </button>
          <button class="agent-action delete">
            <i class="fas fa-trash"></i> Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Agent Card 3 -->
  <div class="col-md-6 col-lg-4">
    <div class="agent-card">
      <div class="agent-card-header">
        <div class="agent-avatar">
          <img src="https://web.trogon.info/thredex/super_admin/images/dummy.png" alt="Agent Avatar">
        </div>
      </div>
      <div class="agent-card-body">
        <h5 class="agent-name">Robert Johnson</h5>
        <p class="agent-title">
          Assistant Manager
          <span class="status-badge status-inactive float-end">Inactive</span>
        </p>
        <div class="agent-stats">
          <div class="agent-stat">
            <div class="agent-stat-value">5</div>
            <div class="agent-stat-label">Buildings</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">18</div>
            <div class="agent-stat-label">Tenants</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">3</div>
            <div class="agent-stat-label">Years</div>
          </div>
        </div>
        <div class="agent-actions">
          <button class="agent-action">
            <i class="fas fa-eye"></i> View
          </button>
          <button class="agent-action">
            <i class="fas fa-edit"></i> Edit
          </button>
          <button class="agent-action delete">
            <i class="fas fa-trash"></i> Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Agent Card 4 -->
  <div class="col-md-6 col-lg-4">
    <div class="agent-card">
      <div class="agent-card-header">
        <div class="agent-avatar">
          <img src="https://web.trogon.info/thredex/super_admin/images/dummy.png" alt="Agent Avatar">
        </div>
      </div>
      <div class="agent-card-body">
        <h5 class="agent-name">Sarah Williams</h5>
        <p class="agent-title">
          Property Manager
          <span class="status-badge status-active float-end">Active</span>
        </p>
        <div class="agent-stats">
          <div class="agent-stat">
            <div class="agent-stat-value">7</div>
            <div class="agent-stat-label">Buildings</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">29</div>
            <div class="agent-stat-label">Tenants</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">4</div>
            <div class="agent-stat-label">Years</div>
          </div>
        </div>
        <div class="agent-actions">
          <button class="agent-action">
            <i class="fas fa-eye"></i> View
          </button>
          <button class="agent-action">
            <i class="fas fa-edit"></i> Edit
          </button>
          <button class="agent-action delete">
            <i class="fas fa-trash"></i> Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Agent Card 5 -->
  <div class="col-md-6 col-lg-4">
    <div class="agent-card">
      <div class="agent-card-header">
        <div class="agent-avatar">
          <img src="https://web.trogon.info/thredex/super_admin/images/dummy.png" alt="Agent Avatar">
        </div>
      </div>
      <div class="agent-card-body">
        <h5 class="agent-name">Michael Brown</h5>
        <p class="agent-title">
          Junior Property Manager
          <span class="status-badge status-pending float-end">Pending</span>
        </p>
        <div class="agent-stats">
          <div class="agent-stat">
            <div class="agent-stat-value">3</div>
            <div class="agent-stat-label">Buildings</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">12</div>
            <div class="agent-stat-label">Tenants</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">1</div>
            <div class="agent-stat-label">Years</div>
          </div>
        </div>
        <div class="agent-actions">
          <button class="agent-action">
            <i class="fas fa-eye"></i> View
          </button>
          <button class="agent-action">
            <i class="fas fa-edit"></i> Edit
          </button>
          <button class="agent-action delete">
            <i class="fas fa-trash"></i> Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Agent Card 6 -->
  <div class="col-md-6 col-lg-4">
    <div class="agent-card">
      <div class="agent-card-header">
        <div class="agent-avatar">
          <img src="https://web.trogon.info/thredex/super_admin/images/dummy.png" alt="Agent Avatar">
        </div>
      </div>
      <div class="agent-card-body">
        <h5 class="agent-name">Emily Davis</h5>
        <p class="agent-title">
          Senior Property Manager
          <span class="status-badge status-active float-end">Active</span>
        </p>
        <div class="agent-stats">
          <div class="agent-stat">
            <div class="agent-stat-value">10</div>
            <div class="agent-stat-label">Buildings</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">38</div>
            <div class="agent-stat-label">Tenants</div>
          </div>
          <div class="agent-stat">
            <div class="agent-stat-value">6</div>
            <div class="agent-stat-label">Years</div>
          </div>
        </div>
        <div class="agent-actions">
          <button class="agent-action">
            <i class="fas fa-eye"></i> View
          </button>
          <button class="agent-action">
            <i class="fas fa-edit"></i> Edit
          </button>
          <button class="agent-action delete">
            <i class="fas fa-trash"></i> Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Pagination -->
<nav aria-label="Agents pagination" class="mt-4">
  <ul class="pagination justify-content-center">
    <li class="page-item disabled">
      <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
    </li>
    <li class="page-item active"><a class="page-link" href="#">1</a></li>
    <li class="page-item"><a class="page-link" href="#">2</a></li>
    <li class="page-item"><a class="page-link" href="#">3</a></li>
    <li class="page-item">
      <a class="page-link" href="#">Next</a>
    </li>
  </ul>
</nav>
<script>
    // Agent card hover effect
    $('.agent-card').hover(
    function() {
        $(this).find('.agent-card-header').css('transform', 'scale(1.02)');
    },
    function() {
        $(this).find('.agent-card-header').css('transform', 'scale(1)');
    }
    );

    // Agent action buttons
    $('.agent-action').on('click', function() {
    const action = $(this).text().trim();
    const agentName = $(this).closest('.agent-card-body').find('.agent-name').text();
    
    if (action.includes('Delete')) {
        if (confirm('Are you sure you want to delete ' + agentName + '?')) {
        alert('Agent ' + agentName + ' has been deleted');
        }
    } else if (action.includes('Edit')) {
        alert('Editing agent: ' + agentName);
    } else if (action.includes('View')) {
        alert('Viewing agent: ' + agentName);
    }
    });

    // Filter functionality
    $('.filter-btn').on('click', function() {
    const searchTerm = $('.search-input input').val().toLowerCase();
    const statusFilter = $('.filter-select').eq(0).val();
    
    alert('Filtering agents with search term: "' + searchTerm + '" and status: "' + statusFilter + '"');
    });
</script>