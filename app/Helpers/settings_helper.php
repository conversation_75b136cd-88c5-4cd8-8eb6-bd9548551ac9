<?php
// Private helper function


if (!function_exists('get_settings')) {
    function get_settings($item) {
        $db = \Config\Database::connect();
        $query = $db->table('settings')->getWhere(['key' => $item]);
        $result = $query->getRow();

        if ($result) {
            return $result->value ?? '';
        }else{
            return '';
        }
    }
}
if (!function_exists('get_frontend_settings')) {
    function get_frontend_settings($item) {
        $db = \Config\Database::connect();
        $query = $db->table('frontend_settings')->getWhere(['key' => $item]);
        $result = $query->getRow();

        if ($result) {
            return $result->value ?? '';
        }else{
            return '';
        }
    }
}

if (!function_exists('get_parent_menu')) {
    function get_parent_menu() {
        $db = \Config\Database::connect();
        $query = $db->table('menu')->getWhere(['parent' => 0]);
        $result = $query->getResultArray();

        if ($result) {
            return $result;
        }else{
            return [];
        }
    }
}
if (!function_exists('get_child_menu')) {
    function get_child_menu($parent_id) {
        $db = \Config\Database::connect();
        $query = $db->table('menu')->getWhere(['parent' => $parent_id]);
        $result = $query->getResultArray();

        if ($result) {
            return $result;
        }else{
            return [];
        }
    }
}



if (!function_exists('get_site_title')){
    function get_site_title() {
        $site_title = _get_session_value('site_title');
        if (empty($site_title)){
            $site_title = get_settings('site_title');
        }
        if (empty($site_title)){
            return '';
        }else{
            return $site_title;
        }
    }
}


if (!function_exists('get_site_logo')){
    function get_site_logo() {
        $site_logo = _get_session_value('site_logo');
        if (empty($site_logo)){
            $site_logo = get_frontend_settings('dark_logo');
        }
        return $site_logo;
    }
}



if (!function_exists('get_favicon')){
    function get_favicon() {
        $favicon = _get_session_value('site_logo');
        if (empty($favicon)){
            $favicon = get_frontend_settings('favicon');
        }
        return $favicon;
    }
}


if ( ! function_exists('themeConfiguration'))
{
    function themeConfiguration($theme, $key = "")
    {
        $themeConfigs = [];
        if (file_exists('assets/frontend/'.$theme.'/config/theme-config.json')) {
            $themeConfigs = file_get_contents('assets/frontend/'.$theme.'/config/theme-config.json');
            $themeConfigs = json_decode($themeConfigs, true);
            if ($key != "") {
                if (array_key_exists($key, $themeConfigs)) {
                    return $themeConfigs[$key];
                } else {
                    return false;
                }
            }else {
                return $themeConfigs;
            }
        } else {
            return false;
        }
    }
}

function cleanHTMLText($text) {
    // Replace &nbsp; and <br> with an empty string
    $cleaned_text = str_replace(['&nbsp;', '<br>'], '', $text);
    return trim($cleaned_text);
}

