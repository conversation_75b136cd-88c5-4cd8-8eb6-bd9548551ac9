<?php
// Private helper function
if (!function_exists('_get_session_value')){
    function _get_session_value($session_key) {
        $session = session();
        return $session->get($session_key);
    }
}

if (!function_exists('get_user_id')){
    function get_user_id() {
        return _get_session_value('user_id');
    }
}

if (!function_exists('get_role_id')){
    function get_role_id() {
        return _get_session_value('role_id');
    }
}

if (!function_exists('get_role_title')){
    function get_role_title() {
        return _get_session_value('role_title');
    }
}

if (!function_exists('get_user_name')){
    function get_user_name() {
        return _get_session_value('user_name');
    }
}

if (!function_exists('get_profile_image')){
    function get_profile_image() {
        return _get_session_value('profile_image');
    }
}

if (!function_exists('is_logged_in')){
    function is_logged_in($role_id = null) {
        $is_logged_in = _get_session_value('logged_in');
        $is_user_id = _get_session_value('user_id') > 0;

        if ($role_id){
            if (get_role_id() != $role_id){
                // return false;
            }
        }
        return $is_user_id && $is_logged_in;
    }
}

if (!function_exists('check_login')){
    function check_login($role_id = null) {
        if (!is_logged_in($role_id)){
            session()->setFlashdata('message_error', 'Session expired, Login Again!');
            if($role_id != 1){
                return redirect()->to(base_url('login'));
            }
            return redirect()->to(base_url('login/admin_login'));
        }
    }
}


if (!function_exists('is_admin')){
    function is_admin() {
        if (get_role_id()==1){
            return true;
        }
        return false;
    }
}

if (!function_exists('is_owner')){
    function is_owner() {
        if (get_role_id()==2){
            return true;
        }
        return false;
    }
}


if (!function_exists('is_agent')){
    function is_agent() {
        if (get_role_id()==3){
            return true;
        }
        return false;
    }
}


if (!function_exists('is_tenant')){
    function is_tenant() {
        if (get_role_id()==4){
            return true;
        }
        return false;
    }
}























