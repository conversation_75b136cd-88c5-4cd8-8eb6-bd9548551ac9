# THREDEX

## Notes:
- We have BaseController Class in the app/Controllers directory which will be extended by User Module Base Module.
- Each user module will have their own base controller class
- All database tables will have their own models and there is a base model class
    - Base Model class: BaseModel.php
    - Each model class will follow the following name strcuture: {Example}Model.php
- While generating the database queries you should always prefer using base model class methods for add, edit, delete, get etc.



We will have the following four user modules:

### 1. Super Admin

- Role Id: 1
- Directory Strcuture:
    - BaseController: App\Controllers\AdminBaseController;
    - Controllers Directory: app\Controllers\Admin
    - Views Directory: app\Views\Admin
- URL Schema
    - example.com/admin/dashboard
    - example.com/admin/*
- ⁠Responsible for overall system management and configuration.
- ⁠Has access to all buildings, users, and settings.

### 2. Building Owner

- Role id: 2
- Directory Strcuture:
    - BaseController: App\Controllers\OwnerBaseController;
    - Controllers Directory: app\Controllers\Owner
    - Views Directory: app\Views\Owner
- URL Schema
    - example.com/owner/dashboard
    - example.com/owner/*
- ⁠Represents the owner of one or more buildings.
- ⁠Can manage their buildings, assign agents, and view related reports.

### 3. Agent

- Role Id: 3
- Directory Strcuture:
    - BaseController: App\Controllers\AgentBaseController;
    - Controllers Directory: app\Controllers\Agent
    - Views Directory: app\Views\Agent
- URL Schema
    - example.com/agent/dashboard
    - example.com/agent/*
- ⁠Assigned by building owners to manage specific buildings.
- ⁠Handles day-to-day operations, tenant management, and maintenance activities.

### 4. Tenant

- Role Id: 4
- Directory Strcuture:
    - BaseController: App\Controllers\TenantBaseController;
    - Controllers Directory: app\Controllers\Tenant
    - Views Directory: app\Views\Tenant
- URL Schema
    - example.com/tenant/dashboard
    - example.com/tenant/*
- ⁠Occupant of a unit within a building.
- Can view rent details, make payments (Just show cheque or bank account details), raise complaints, and receive notifications.


## URL Schema:

We are using auto routing of CodeIgniter 4 for this project

## Services:
We should use CodeIgniter 4 Services wherever it is possible
- Directory Structure
    - Shared Services: App\Services\Shared
    - Admin Services: App\Services\Admin
    - Owner Services: App\Services\Owner
    - Agent Services: App\Services\Agent
    - Tenant Services: App\Services\Tenant
- When We Should Use Services
    - Multiple Models Need to Work Together
    - When Need to Reuse the Logic in Multiple Places
    - To Keep Controllers Slim and Focused
    - Business Logic Is Too Complex for a Model
    - To Avoid put payment calculations or conditional workflows in models.
    - The Logic Isn’t Tied to a Single Table
    - Need Role-Based Variations in Logic
    - When Performing with External Integrations
    - Reusable but Not DB-Centric
- Examples:
    - Tenant onboarding
    - Monthly rent generation
    - Complaint handling
    - Owner-level reports


 
